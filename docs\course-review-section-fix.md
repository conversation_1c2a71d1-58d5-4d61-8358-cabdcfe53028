# 课程详情评价区域样式和功能修复

## 问题描述

用户反馈：
> "这里的样式还是有问题，直接照抄咨询师详情里面的，咨询师详情里面关于评价的功能也要实现"

**问题分析**：
- 课程详情页面的评价区域样式与咨询师详情不一致
- 缺少评价统计功能（平均评分、总评价数）
- 缺少"更多"按钮跳转到完整评价页面
- 评价数据结构和显示方式需要优化

## 修复方案

### 1. 新增API接口

**添加课程评价统计接口**：
```javascript
// 获取课程评价统计
export function getCourseReviewStats(courseId) {
  return request({
    url: `/miniapp/course/review-stats/${courseId}`,
    method: 'get'
  })
}
```

### 2. 数据结构优化

**添加评价统计数据**：
```javascript
// 评价相关数据
const reviewStatistics = ref({
  avgRating: 0,
  totalCount: 0,
  distribution: {}
})
const recentReviews = ref([])
```

### 3. HTML结构重构

**采用咨询师详情页面的评价结构**：
```vue
<!-- 用户评价区域 -->
<view id="section-2" class="content-section">
  <view class="section-title review-title">
    <view class="title-text">用户评价</view>
    <view class="more-btn" @click="goToReviewsPage" v-if="reviewStatistics.totalCount > 0">
      <text>更多</text>
      <image mode="widthFix" src="/static/icon/圆角矩形 1.png"></image>
    </view>
  </view>

  <view class="review-summary" v-if="reviewStatistics.totalCount > 0">
    <view class="rating-overview">
      <view class="avg-rating">
        <text class="rating-number">{{ reviewStatistics.avgRating || '0.0' }}</text>
        <uni-rate color="#E6E6E6" active-color='#A04571'
          :value="parseFloat(reviewStatistics.avgRating || 0)" size="14" readonly />
      </view>
      <text class="total-count">共{{ reviewStatistics.totalCount }}条评价</text>
    </view>
    <view class="recent-reviews">
      <ReviewItem v-for="review in recentReviews.slice(0, 2)" :key="review.id" :review="review"
        :content-max-length="50" :showCard="false" />
    </view>
  </view>

  <view class="info-content" v-else>
    暂无评价数据
  </view>
</view>
```

### 4. 功能实现

**数据加载优化**：
```javascript
const loadReviewList = async () => {
  try {
    // 加载评价统计
    const statsRes = await getCourseReviewStats(courseId.value)
    if (statsRes.code === 200) {
      reviewStatistics.value = statsRes.data || {}
    }

    // 加载评价列表
    const res = await getCourseReviews(courseId.value)
    if (res.code === 200) {
      reviewList.value = res.data || []
      // 取前2条作为最新评价显示
      recentReviews.value = (res.data || []).slice(0, 2)
    }
  } catch (error) {
    console.error('获取评价数据失败:', error)
  }
}
```

**跳转功能**：
```javascript
// 跳转到评价页面
const goToReviewsPage = () => {
  uni.navigateTo({
    url: `/pages/course/reviews/index?courseId=${courseId.value}`
  })
}
```

### 5. 样式统一

**完全采用咨询师详情的评价样式**：
```scss
// 评价标题样式
.review-title {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  .more-btn {
    display: flex;
    align-items: center;
    gap: 8rpx;
    font-size: 26rpx;
    color: #666;

    image {
      width: 16rpx;
      height: 16rpx;
    }
  }
}

// 评价相关样式
.review-summary {
  .rating-overview {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;

    .avg-rating {
      display: flex;
      align-items: center;
      gap: 16rpx;

      .rating-number {
        font-size: 36rpx;
        font-weight: bold;
        color: #A04571;
      }
    }

    .total-count {
      font-size: 24rpx;
      color: #666;
    }
  }

  .recent-reviews {
    margin-bottom: 24rpx;
  }
}
```

## 实现特点

### 🎯 **完全一致的设计**
- **标题样式**：与咨询师详情完全相同的标题和"更多"按钮
- **评分显示**：使用uni-rate组件，颜色和尺寸保持一致
- **布局结构**：评价概览和最新评价的布局完全统一

### 📊 **丰富的评价信息**
- **平均评分**：显示精确的平均评分数值
- **评价总数**：显示总评价条数
- **最新评价**：展示最新的2条评价
- **评分分布**：支持评分分布统计（预留接口）

### 🚀 **完整的交互功能**
- **更多按钮**：点击跳转到完整评价页面
- **条件显示**：有评价时显示详细信息，无评价时显示空状态
- **懒加载**：点击评价tab时才加载数据

### 🎨 **统一的视觉体验**
- **颜色主题**：评分颜色使用品牌色#A04571
- **字体大小**：与咨询师详情保持一致的字体层级
- **间距布局**：相同的内边距和外边距设置

## 技术要点

### 📡 **API集成**
- 新增`getCourseReviewStats`接口获取评价统计
- 优化`loadReviewList`方法同时加载统计和列表数据
- 支持评价分布数据的扩展

### 🔄 **数据处理**
- 评价统计数据的结构化存储
- 最新评价的截取和显示
- 空状态的优雅处理

### 🎯 **用户体验**
- 评价信息的层次化展示
- 快速跳转到详细评价页面
- 与其他页面保持一致的交互模式

## 测试验证

### ✅ **样式一致性**
1. 评价标题样式与咨询师详情完全一致
2. 评分显示颜色和尺寸正确
3. "更多"按钮样式和位置正确

### ✅ **功能完整性**
1. 评价统计数据正确加载和显示
2. 最新评价列表正确展示
3. "更多"按钮跳转功能正常

### ✅ **数据处理**
1. 有评价时显示详细统计信息
2. 无评价时显示友好的空状态提示
3. API调用失败时的错误处理

### ✅ **交互体验**
1. 点击评价tab时自动加载数据
2. 评价信息展示清晰易读
3. 跳转到详细评价页面流畅

现在课程详情页面的评价区域已经与咨询师详情页面完全一致，包括样式、功能和交互体验！
