<template>
  <view class="video-call-container">
    <!-- 远端视频 -->
    <view class="remote-video-container">
      <live-player
        v-if="remoteStreamUrl"
        :src="remoteStreamUrl"
        mode="RTC"
        autoplay
        muted="false"
        class="remote-video"
        @statechange="onRemoteStateChange"
        @error="onRemoteError"
      />
      <view v-else class="waiting-container">
        <image class="avatar" :src="remoteUserAvatar" mode="aspectFill" />
        <text class="waiting-text">等待对方加入...</text>
      </view>
    </view>

    <!-- 本地视频 -->
    <view class="local-video-container">
      <live-pusher
        v-if="localStreamUrl"
        :url="localStreamUrl"
        mode="RTC"
        autopush
        :muted="isMuted"
        :enable-camera="isVideoEnabled"
        class="local-video"
        @statechange="onLocalStateChange"
        @error="onLocalError"
      />
    </view>

    <!-- 通话信息 -->
    <view class="call-info">
      <text class="caller-name">{{ remoteUserName }}</text>
      <text class="call-duration">{{ formatDuration(callDuration) }}</text>
      <text class="call-status">{{ callStatusText }}</text>
    </view>

    <!-- 控制按钮 -->
    <view class="control-buttons">
      <view class="button-row">
        <!-- 静音按钮 -->
        <view 
          class="control-btn" 
          :class="{ active: isMuted }"
          @click="toggleMute"
        >
          <image :src="isMuted ? '/static/icon/mic-off.png' : '/static/icon/mic-on.png'" />
          <text>{{ isMuted ? '取消静音' : '静音' }}</text>
        </view>

        <!-- 摄像头按钮 -->
        <view 
          class="control-btn" 
          :class="{ active: !isVideoEnabled }"
          @click="toggleVideo"
        >
          <image :src="isVideoEnabled ? '/static/icon/video-on.png' : '/static/icon/video-off.png'" />
          <text>{{ isVideoEnabled ? '关闭摄像头' : '开启摄像头' }}</text>
        </view>

        <!-- 切换摄像头 -->
        <view class="control-btn" @click="switchCamera">
          <image src="/static/icon/camera-switch.png" />
          <text>切换</text>
        </view>
      </view>

      <view class="button-row">
        <!-- 挂断按钮 -->
        <view class="control-btn hangup-btn" @click="hangUp">
          <image src="/static/icon/hangup.png" />
          <text>挂断</text>
        </view>
      </view>
    </view>

    <!-- 网络状态提示 -->
    <view v-if="networkQuality < 3" class="network-warning">
      <text>网络信号较弱，可能影响通话质量</text>
    </view>
  </view>
</template>

<script>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useUserStore } from '@/stores/user'

export default {
  name: 'VideoCall',
  setup() {
    const userStore = useUserStore()
    
    // 响应式数据
    const channelId = ref('')
    const appointmentId = ref('')
    const remoteUserId = ref('')
    const remoteUserName = ref('')
    const remoteUserAvatar = ref('')
    const localStreamUrl = ref('')
    const remoteStreamUrl = ref('')
    const callDuration = ref(0)
    const callStatus = ref('connecting') // connecting, connected, disconnected
    const isMuted = ref(false)
    const isVideoEnabled = ref(true)
    const networkQuality = ref(5)
    
    // RTC相关
    let rtcClient = null
    let callTimer = null
    
    // 计算属性
    const callStatusText = computed(() => {
      switch (callStatus.value) {
        case 'connecting': return '连接中...'
        case 'connected': return '通话中'
        case 'disconnected': return '已断开'
        default: return ''
      }
    })
    
    // 页面加载
    onMounted(async () => {
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const options = currentPage.options
      
      channelId.value = options.channelId
      appointmentId.value = options.appointmentId
      remoteUserId.value = options.remoteUserId
      remoteUserName.value = options.remoteUserName || '对方'
      remoteUserAvatar.value = options.remoteUserAvatar || '/static/icon/default-avatar.png'
      
      await initRTC()
    })
    
    // 页面卸载
    onUnmounted(() => {
      cleanup()
    })
    
    // 初始化RTC
    const initRTC = async () => {
      try {
        // 获取RTC Token
        const tokenRes = await uni.request({
          url: '/api/rtc/token',
          method: 'POST',
          data: {
            channelId: channelId.value,
            userId: userStore.userId,
            role: userStore.isConsultant ? 'consultant' : 'client'
          }
        })
        
        if (tokenRes.data.code === 200) {
          const { token } = tokenRes.data.data
          await joinChannel(token)
        } else {
          throw new Error('获取Token失败')
        }
      } catch (error) {
        console.error('初始化RTC失败:', error)
        uni.showToast({
          title: '连接失败',
          icon: 'none'
        })
      }
    }
    
    // 加入频道
    const joinChannel = async (token) => {
      try {
        callStatus.value = 'connecting'
        
        // 这里需要根据实际的RTC SDK进行调用
        // 小程序可能需要使用live-pusher和live-player组件
        
        // 开始计时
        startCallTimer()
        
        callStatus.value = 'connected'
        
        uni.showToast({
          title: '连接成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('加入频道失败:', error)
        callStatus.value = 'disconnected'
      }
    }
    
    // 开始通话计时
    const startCallTimer = () => {
      callTimer = setInterval(() => {
        callDuration.value++
      }, 1000)
    }
    
    // 格式化通话时长
    const formatDuration = (seconds) => {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = seconds % 60
      
      if (hours > 0) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      } else {
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      }
    }
    
    // 切换静音
    const toggleMute = () => {
      isMuted.value = !isMuted.value
      // 调用RTC SDK的静音方法
    }
    
    // 切换视频
    const toggleVideo = () => {
      isVideoEnabled.value = !isVideoEnabled.value
      // 调用RTC SDK的视频开关方法
    }
    
    // 切换摄像头
    const switchCamera = () => {
      // 调用RTC SDK的摄像头切换方法
    }
    
    // 挂断通话
    const hangUp = async () => {
      try {
        // 结束通话记录
        await uni.request({
          url: '/api/rtc/end-call',
          method: 'POST',
          data: {
            channelId: channelId.value,
            duration: callDuration.value
          }
        })
        
        cleanup()
        
        uni.navigateBack()
      } catch (error) {
        console.error('挂断失败:', error)
        uni.navigateBack()
      }
    }
    
    // 清理资源
    const cleanup = () => {
      if (callTimer) {
        clearInterval(callTimer)
        callTimer = null
      }
      
      if (rtcClient) {
        // 离开频道和清理RTC资源
        rtcClient = null
      }
      
      callStatus.value = 'disconnected'
    }
    
    // 事件处理
    const onLocalStateChange = (event) => {
      console.log('本地推流状态变化:', event)
    }
    
    const onRemoteStateChange = (event) => {
      console.log('远端播放状态变化:', event)
    }
    
    const onLocalError = (event) => {
      console.error('本地推流错误:', event)
    }
    
    const onRemoteError = (event) => {
      console.error('远端播放错误:', event)
    }
    
    return {
      remoteUserName,
      remoteUserAvatar,
      localStreamUrl,
      remoteStreamUrl,
      callDuration,
      callStatusText,
      isMuted,
      isVideoEnabled,
      networkQuality,
      formatDuration,
      toggleMute,
      toggleVideo,
      switchCamera,
      hangUp,
      onLocalStateChange,
      onRemoteStateChange,
      onLocalError,
      onRemoteError
    }
  }
}
</script>

<style scoped lang="scss">
.video-call-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-color: #000;
  overflow: hidden;
}

.remote-video-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  
  .remote-video {
    width: 100%;
    height: 100%;
  }
  
  .waiting-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    
    .avatar {
      width: 200rpx;
      height: 200rpx;
      border-radius: 100rpx;
      margin-bottom: 40rpx;
    }
    
    .waiting-text {
      color: #fff;
      font-size: 32rpx;
    }
  }
}

.local-video-container {
  position: absolute;
  top: 100rpx;
  right: 40rpx;
  width: 240rpx;
  height: 320rpx;
  border-radius: 20rpx;
  overflow: hidden;
  border: 4rpx solid #fff;
  
  .local-video {
    width: 100%;
    height: 100%;
  }
}

.call-info {
  position: absolute;
  top: 100rpx;
  left: 40rpx;
  
  .caller-name {
    display: block;
    color: #fff;
    font-size: 36rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
  }
  
  .call-duration {
    display: block;
    color: #fff;
    font-size: 28rpx;
    margin-bottom: 10rpx;
  }
  
  .call-status {
    display: block;
    color: #ccc;
    font-size: 24rpx;
  }
}

.control-buttons {
  position: absolute;
  bottom: 100rpx;
  left: 0;
  right: 0;
  padding: 0 40rpx;
  
  .button-row {
    display: flex;
    justify-content: center;
    margin-bottom: 40rpx;
    
    .control-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 120rpx;
      height: 120rpx;
      margin: 0 30rpx;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 60rpx;
      
      &.active {
        background-color: #ff4757;
      }
      
      &.hangup-btn {
        background-color: #ff4757;
        width: 140rpx;
        height: 140rpx;
        border-radius: 70rpx;
      }
      
      image {
        width: 48rpx;
        height: 48rpx;
        margin-bottom: 10rpx;
      }
      
      text {
        color: #fff;
        font-size: 20rpx;
      }
    }
  }
}

.network-warning {
  position: absolute;
  top: 40rpx;
  left: 40rpx;
  right: 40rpx;
  padding: 20rpx;
  background-color: rgba(255, 193, 7, 0.9);
  border-radius: 10rpx;
  
  text {
    color: #fff;
    font-size: 24rpx;
  }
}
</style>
