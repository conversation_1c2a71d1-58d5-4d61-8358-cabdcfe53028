<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>冥想播放页面预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(180deg, #E8F4FD 0%, #B8E0FF 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .phone-frame {
            width: 375px;
            height: 667px;
            background: linear-gradient(180deg, #E8F4FD 0%, #B8E0FF 100%);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            position: relative;
        }
        
        .meditation-player {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 60px 30px 40px;
        }
        
        .cover-container {
            margin-bottom: 30px;
        }
        
        .cover-wrapper {
            position: relative;
            width: 200px;
            height: 200px;
            border-radius: 50%;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
        
        .cover-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><defs><linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23ff9a9e;stop-opacity:1" /><stop offset="100%" style="stop-color:%23fecfef;stop-opacity:1" /></linearGradient></defs><rect width="200" height="200" fill="url(%23grad)"/><text x="100" y="100" text-anchor="middle" dy="0.3em" fill="white" font-size="16" font-family="Arial">冥想封面</text></svg>') center/cover;
        }
        
        .play-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(1px);
            cursor: pointer;
        }
        
        .play-icon {
            width: 40px;
            height: 40px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M8 5v14l11-7z"/></svg>') center/contain no-repeat;
        }
        
        .meditation-info {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .meditation-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .meditation-subtitle {
            font-size: 14px;
            color: #666;
        }
        
        .progress-section {
            width: 100%;
            margin-bottom: 40px;
        }
        
        .time-display {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 13px;
            color: #666;
        }
        
        .progress-bar {
            width: 100%;
            height: 30px;
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        
        .progress-track {
            position: relative;
            width: 100%;
            height: 3px;
            background-color: #E5E5E5;
            border-radius: 1.5px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #FF6B9D 0%, #FF8A80 100%);
            border-radius: 1.5px;
            width: 35%;
            transition: width 0.1s ease;
        }
        
        .progress-thumb {
            position: absolute;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 10px;
            height: 10px;
            background: linear-gradient(90deg, #FF6B9D 0%, #FF8A80 100%);
            border-radius: 50%;
            box-shadow: 0 1px 4px rgba(255, 107, 157, 0.3);
            left: 35%;
        }
        
        .control-section {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            padding: 0 20px;
        }
        
        .side-controls .side-btn {
            width: 40px;
            height: 40px;
            background: none;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .side-icon {
            width: 24px;
            height: 24px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23666"><circle cx="12" cy="12" r="10"/></svg>') center/contain no-repeat;
        }
        
        .main-controls {
            display: flex;
            align-items: center;
            gap: 30px;
        }
        
        .control-btn {
            width: 40px;
            height: 40px;
            background: none;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .control-icon {
            width: 24px;
            height: 24px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23666"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>') center/contain no-repeat;
        }
        
        .play-btn-main {
            width: 50px;
            height: 50px;
            background: none;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .play-icon-main {
            width: 30px;
            height: 30px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23333"><path d="M8 5v14l11-7z"/></svg>') center/contain no-repeat;
        }
        
        .preview-note {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="phone-frame">
        <div class="preview-note">冥想播放页面预览</div>
        <div class="meditation-player">
            <div class="cover-container">
                <div class="cover-wrapper">
                    <div class="cover-image"></div>
                    <div class="play-overlay">
                        <div class="play-icon"></div>
                    </div>
                </div>
            </div>
            
            <div class="meditation-info">
                <div class="meditation-title">高山流水</div>
                <div class="meditation-subtitle">大自然冥想 深度催眠 放松解压</div>
            </div>
            
            <div class="progress-section">
                <div class="time-display">
                    <span class="current-time">02:50</span>
                    <span class="total-time">05:15</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-track">
                        <div class="progress-fill"></div>
                        <div class="progress-thumb"></div>
                    </div>
                </div>
            </div>
            
            <div class="control-section">
                <div class="side-controls left">
                    <button class="side-btn">
                        <div class="side-icon"></div>
                    </button>
                </div>
                
                <div class="main-controls">
                    <button class="control-btn">
                        <div class="control-icon"></div>
                    </button>
                    
                    <button class="play-btn-main">
                        <div class="play-icon-main"></div>
                    </button>
                    
                    <button class="control-btn">
                        <div class="control-icon"></div>
                    </button>
                </div>
                
                <div class="side-controls right">
                    <button class="side-btn">
                        <div class="side-icon"></div>
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
