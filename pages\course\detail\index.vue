<template>
  <view class="course-detail">
    <!-- 课程封面 -->
    <view class="course-header">
      <image :src="courseDetail.coverImage || defaultCover" mode="aspectFill" class="cover-image"></image>
    </view>
    <view class="course-content">
      <!-- 课程基本信息 -->
      <view class="course-info-section">
        <view class="course-title">{{ courseDetail.title || '课程标题' }}</view>
        <view class="price-section">
          <view v-if="courseDetail.isFree !== 1" class="price-info">
            <text class="price">¥{{ courseDetail.price || 0 }}</text>
            <text class="price-unit">/次</text>
          </view>
          <text class="sales-count">已售{{ courseDetail.salesCount || 9999 }}+</text>
        </view>
      </view>

      <!-- 讲师卡片 -->
      <view class="instructor-card" v-if="courseDetail.instructor">
        <image :src="courseDetail.instructor.avatar || defaultAvatar" mode="aspectFill" class="instructor-avatar">
        </image>
        <view class="instructor-info">
          <view class="instructor-name-title">
            <view class="instructor-name">{{ courseDetail.instructor.name || '讲师姓名' }}</view>
            <view class="instructor-title">{{ courseDetail.instructor.title || '资深咨询师' }}</view>
          </view>
          <view class="instructor-desc">{{ courseDetail.instructor.qualifications || '高校心理咨询师 | 国家二级心理咨询师' }}</view>
        </view>
      </view>

      <!-- 滑动标签页 -->
      <view class="sticky-tabs">
        <scroll-view scroll-x class="tab-scroll">
          <view class="tab-list">
            <view v-for="(tab, index) in tabs" :key="index" :class="['tab-item', { active: currentTab === index }]"
              @click="scrollToSection(index)">
              {{ tab.name }}
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 内容区域 -->
      <scroll-view class="content-area" scroll-y @scroll="handleScroll" :scroll-top="scrollTop">
        <!-- 课程介绍区域 -->
        <view id="section-0" class="content-section">
          <view class="section-title">课程介绍</view>
          <view class="course-description">{{ courseDetail.summary || courseDetail.description || '暂无介绍' }}</view>

          <view class="course-features">
            <view class="feature-item">
              <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
              <text>专业讲师授课</text>
            </view>
            <view class="feature-item">
              <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
              <text>系统化学习路径</text>
            </view>
            <view class="feature-item">
              <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
              <text>实战案例分析</text>
            </view>
          </view>
        </view>

        <!-- 课程章节区域 -->
        <view id="section-1" class="content-section">
          <view class="section-title">课程目录</view>
          <view class="chapter-list" v-if="chapterList.length > 0">
            <view v-for="(chapter, index) in chapterList" :key="chapter.id" class="chapter-item">
              <view class="chapter-header">
                <view class="chapter-number">{{ String(index + 1).padStart(2, '0') }}</view>
                <view class="chapter-info">
                  <view class="chapter-title">{{ chapter.chapterTitle || chapter.title }}</view>
                  <view class="chapter-duration">{{ formatDuration(chapter.duration) }}</view>
                </view>
                <view class="chapter-status">
                  <uni-icons v-if="chapter.completed" type="checkmarkempty" size="20" color="#4CAF50"></uni-icons>
                  <uni-icons v-else-if="chapter.locked" type="locked" size="20" color="#999"></uni-icons>
                  <uni-icons v-else type="right" size="16" color="#999"></uni-icons>
                </view>
              </view>
              <view v-if="chapter.description" class="chapter-description">{{ chapter.description }}</view>
            </view>
          </view>

          <view v-else class="empty-state">
            <uni-icons type="info" size="48" color="#ccc"></uni-icons>
            <text>暂无章节信息</text>
          </view>
        </view>

        <!-- 用户评价区域 -->
        <view id="section-2" class="content-section">
          <view class="section-title">用户评价</view>

          <!-- 评价统计 -->
          <view class="rating-summary">
            <view class="rating-score">
              <text class="score-number">{{ courseDetail.ratingAvg || 5.0 }}</text>
              <view class="stars-container">
                <image v-for="i in 5" :key="i"
                  :src="i <= Math.floor(courseDetail.ratingAvg || 5) ? '/static/icon/star-filled.png' : '/static/icon/star-empty.png'"
                  mode="aspectFit" class="star-icon"></image>
              </view>
              <text class="total-reviews">基于{{ courseDetail.ratingCount || 0 }}条评价</text>
            </view>
          </view>

          <!-- 评价列表 -->
          <view v-if="reviewList.length > 0" class="reviews-list">
            <ReviewItem v-for="review in reviewList" :key="review.id" :review="review" :show-course-info="false" />
          </view>

          <view v-else class="empty-state">
            <uni-icons type="info" size="48" color="#ccc"></uni-icons>
            <text>暂无评价数据</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部操作栏 -->
    <UniversalGoodsNav style="z-index: 1;" page-type="course" :detail-data="courseDetail"
      :purchased="courseDetail.purchased" :price="courseDetail.price" :favorited="courseDetail.favorited"
      :favorite-id="courseDetail.favoriteId" @favorite="handleFavorite" @contact-service="handleContactService"
      @share="handleShare" @main-action="handleMainAction" />

    <!-- 支付弹框 -->
    <PaymentModal ref="paymentModal" :order-info="orderInfo" @close="onPaymentClose" @pay-success="onPaymentSuccess"
      @pay-fail="onPaymentFail" />
  </view>
</template>

<script setup>
import { ref, computed, getCurrentInstance, onUnmounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import {
  getCourseDetail,
  getCourseChapters,
  getCourseReviews,
  createCourseOrder
} from '@/api/course'
import { useUserStore } from '@/stores/user'
import PaymentModal from '@/components/PaymentModal/PaymentModal.vue'
import UniversalGoodsNav from '@/components/UniversalGoodsNav/UniversalGoodsNav.vue'
import ReviewItem from '@/components/ReviewItem/ReviewItem.vue'


// 响应式数据
const courseDetail = ref({})
const chapterList = ref([])
const reviewList = ref([])
const tabs = [
  { name: '课程介绍', id: 'section-0' },
  { name: '课程目录', id: 'section-1' },
  { name: '用户评价', id: 'section-2' }
]
const currentTab = ref(0)
const courseId = ref(null)
const paymentModal = ref(null)
const orderInfo = ref({})
const expandedChapters = ref([]) // 展开的章节ID列表
const scrollTop = ref(0)

// 默认图片
const defaultCover = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png'
const defaultAvatar = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-avatar.png'

const userStore = useUserStore()

// 计算属性
const parsedTags = computed(() => {
  if (!courseDetail.value.tags) return []
  try {
    return JSON.parse(courseDetail.value.tags)
  } catch (error) {
    return []
  }
})

// 章节展开/收起控制
const toggleChapter = (chapterId) => {
  const index = expandedChapters.value.indexOf(chapterId)
  if (index > -1) {
    // 如果已展开，则收起
    expandedChapters.value.splice(index, 1)
  } else {
    // 如果未展开，则展开
    expandedChapters.value.push(chapterId)
  }
}

// 播放子章节视频
const playSubChapter = (subChapter) => {
  // 检查是否可以观看
  if (subChapter.isTrial !== 1 && !courseDetail.value.purchased && courseDetail.value.isFree !== 1 && courseDetail.value.price > 0) {
    uni.showToast({
      title: '请先购买课程',
      icon: 'none'
    })
    return
  }

  // 检查是否有视频
  if (!subChapter.mediaUrl) {
    uni.showToast({
      title: '该章节暂无视频',
      icon: 'none'
    })
    return
  }

  // 播放视频
  playVideo(subChapter)
}

// 格式化时长（秒转换为分钟）
const formatDuration = (seconds) => {
  if (!seconds) return '0分钟'
  const minutes = Math.floor(seconds / 60)
  return `${minutes}分钟`
}

// 处理收藏事件
const handleFavorite = (favoriteData) => {
  courseDetail.value.favorited = favoriteData.favorited
  courseDetail.value.favoriteId = favoriteData.favoriteId
}

// 处理客服事件
const handleContactService = () => {
  console.log('联系客服')
}

// 处理分享事件
const handleShare = (shareConfig) => {
  console.log('分享配置:', shareConfig)
  uni.showToast({
    title: '转发成功',
    icon: 'success'
  })
}

// 处理主要操作事件
const handleMainAction = ({ pageType }) => {
  if (!courseDetail.value.purchased && courseDetail.value.isFree !== 1 && courseDetail.value.price > 0) {
    buyCourse()
  } else {
    startStudy()
  }
}

// 滑动到指定区域
const scrollToSection = (index) => {
  currentTab.value = index

  // 获取目标元素
  const query = uni.createSelectorQuery()
  query.select(`#${tabs[index].id}`).boundingClientRect()
  query.exec((res) => {
    if (res[0]) {
      scrollTop.value = res[0].top
    }
  })

  // 加载对应数据
  if (index === 1 && chapterList.value.length === 0) {
    loadChapterList()
  }
  if (index === 2 && reviewList.value.length === 0) {
    loadReviewList()
  }
}

// 处理滚动事件
const handleScroll = (e) => {
  // 根据滚动位置更新当前tab
  const scrollTop = e.detail.scrollTop

  // 获取各个区域的位置
  const query = uni.createSelectorQuery()
  tabs.forEach((tab, index) => {
    query.select(`#${tab.id}`).boundingClientRect()
  })

  query.exec((res) => {
    let activeIndex = 0
    res.forEach((rect, index) => {
      if (rect && scrollTop >= rect.top - 100) {
        activeIndex = index
      }
    })
    currentTab.value = activeIndex
  })
}

const loadCourseDetail = async () => {
  try {
    const res = await getCourseDetail(courseId.value)
    if (res.code === 200) {
      courseDetail.value = res.data

      // 处理章节数据 - 构建层级结构
      if (res.data.chapters && res.data.chapters.length > 0) {
        const processedChapters = processChapterHierarchy(res.data.chapters)
        chapterList.value = processedChapters

        // 默认展开第一个章节
        if (processedChapters.length > 0) {
          expandedChapters.value.push(processedChapters[0].id)
        }
      }

      // 设置导航标题
      uni.setNavigationBarTitle({
        title: res.data.title || '课程详情'
      })

      console.log('课程详情数据:', courseDetail.value)
      console.log('章节数据:', chapterList.value)
    } else {
      uni.showToast({
        title: res.msg || '获取课程详情失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取课程详情失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

// 处理章节层级结构
const processChapterHierarchy = (chapters) => {
  if (!chapters || chapters.length === 0) return []

  // 按照 level 和 chapterOrder 排序
  const sortedChapters = [...chapters].sort((a, b) => {
    if (a.level !== b.level) {
      return a.level - b.level
    }
    return (a.chapterOrder || 0) - (b.chapterOrder || 0)
  })

  const result = []
  const parentMap = new Map()

  sortedChapters.forEach(chapter => {
    if (chapter.level === 1) {
      // 一级章节
      chapter.children = []
      result.push(chapter)
      parentMap.set(chapter.id, chapter)
    } else if (chapter.level === 2) {
      // 二级章节
      const parent = parentMap.get(chapter.parentId)
      if (parent) {
        parent.children.push(chapter)
      } else {
        // 如果找不到父章节，作为一级章节处理
        result.push(chapter)
      }
    }
  })

  return result
}

const loadChapterList = async () => {
  // 如果已经从课程详情中获取了章节，就不需要再次请求
  if (chapterList.value.length > 0) {
    return
  }

  try {
    const res = await getCourseChapters(courseId.value)
    if (res.code === 200) {
      chapterList.value = res.data || []
    }
  } catch (error) {
    console.error('获取章节列表失败:', error)
  }
}

const loadReviewList = async () => {
  try {
    const res = await getCourseReviews(courseId.value)
    if (res.code === 200) {
      reviewList.value = res.data || []
    }
  } catch (error) {
    console.error('获取评价列表失败:', error)
  }
}

const buyCourse = async () => {
  if (!userStore.isLoggedIn) {
    uni.navigateTo({
      url: '/pages/login/login'
    })
    return
  }

  try {
    uni.showLoading({ title: '创建订单中...' })

    const res = await createCourseOrder(courseId.value)

    uni.hideLoading()

    if (res.code === 200) {
      // 设置订单信息并打开支付弹框
      orderInfo.value = {
        orderNo: res.data.orderNo,
        product: courseDetail.value
      }
      paymentModal.value?.open()
    } else {
      uni.showToast({
        title: res.msg || '创建订单失败',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('创建订单失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

const startStudy = () => {
  // 查找第一个有视频且可以播放的子章节
  for (const chapter of chapterList.value) {
    if (chapter.children && chapter.children.length > 0) {
      for (const subChapter of chapter.children) {
        if (subChapter.mediaUrl && (subChapter.isTrial === 1 || courseDetail.value.purchased || courseDetail.value.isFree === 1 || courseDetail.value.price == 0)) {
          // 自动展开该章节
          if (!expandedChapters.value.includes(chapter.id)) {
            expandedChapters.value.push(chapter.id)
          }
          playVideo(subChapter)
          return
        }
      }
    }
  }

  // 如果没有找到可播放的视频，提示用户
  uni.showToast({
    title: '暂无可播放的视频章节',
    icon: 'none'
  })
}

// 播放视频
const playVideo = (chapter) => {
  // 使用uni-app的视频播放器
  uni.navigateTo({
    url: `/pages/course/video-player/index?videoUrl=${encodeURIComponent(chapter.mediaUrl)}&title=${encodeURIComponent(chapter.chapterTitle)}&chapterId=${chapter.id}&courseId=${courseId.value}`
  })
}

const formatDate = (dateStr) => {
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 支付弹框事件处理
const onPaymentClose = () => {
  console.log('支付弹框关闭')
}

const onPaymentSuccess = (paymentData) => {
  console.log('支付成功:', paymentData)

  // 更新课程购买状态
  courseDetail.value.purchased = true

  // 重新加载课程详情
  loadCourseDetail()
}

const onPaymentFail = (error) => {
  console.log('支付失败:', error)
}

// 生命周期
onLoad((options) => {
  courseId.value = options.id
  console.log('课程ID:', courseId.value);

  if (options.id) {
    loadCourseDetail()
  } else {
    // 如果没有ID，使用测试数据
    loadTestData()
  }
})

// 加载测试数据（用于调试）
const loadTestData = () => {
  const testData = {
    "id": 1000,
    "title": "情绪管理入门",
    "summary": "学会识别和调节自己的情绪",
    "price": 199,
    "salesCount": 1,
    "chapterCount": 6,
    "trialChapterCount": 2,
    "instructorId": 100,
    "coverImage": "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png",
    "difficultyLevel": 1,
    "durationTotal": 8700,
    "viewCount": 22,
    "ratingAvg": 4.3,
    "ratingCount": 2,
    "isFree": 1,
    "tags": "[\"情绪管理\",\"压力缓解\"]",
    "status": 1,
    "instructor": {
      "id": 100,
      "name": "张老师",
      "title": "资深心理咨询师",
      "qualifications": "国家一级心理咨询师，十年从业经验"
    },
    "chapters": [
      {
        "id": 2000,
        "level": 1,
        "chapterTitle": "第一章 情绪认知",
        "duration": 1800,
        "chapterOrder": 1,
        "isTrial": 1,
        "children": []
      },
      {
        "id": 2003,
        "level": 1,
        "chapterTitle": "第二章 情绪调节方法",
        "duration": 2700,
        "chapterOrder": 2,
        "isTrial": 0,
        "children": []
      },
      {
        "id": 2001,
        "parentId": 2000,
        "level": 2,
        "chapterTitle": "1.1 情绪的基本类型",
        "duration": 1200,
        "chapterOrder": 1,
        "isTrial": 1,
        "mediaUrl": "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/video/%E6%88%91%E6%98%AF%E5%A6%82%E4%BD%95%E5%9C%A8B%E7%AB%99%E8%87%AA%E5%AD%A6%E5%BF%83%E7%90%86%E5%AD%A6%E7%9A%84_%E4%B8%A8%E5%BF%83%E7%90%86%E5%AD%A6%E4%BB%8E%E5%85%A5%E9%97%A8%E5%88%B0%E5%85%A5%E5%9C%9F%20%E2%80%94%E2%80%94%20B%E7%AB%99%E8%87%AA%E5%AD%A6%E5%BF%83%E7%90%86%E5%AD%A6.mp4"
      },
      {
        "id": 2004,
        "parentId": 2003,
        "level": 2,
        "chapterTitle": "2.1 呼吸调节法",
        "duration": 600,
        "chapterOrder": 1,
        "isTrial": 0
      },
      {
        "id": 2002,
        "parentId": 2000,
        "level": 2,
        "chapterTitle": "1.2 情绪与身体的关系",
        "duration": 900,
        "chapterOrder": 2,
        "isTrial": 0
      },
      {
        "id": 2005,
        "parentId": 2003,
        "level": 2,
        "chapterTitle": "2.2 认知重构法",
        "duration": 1500,
        "chapterOrder": 2,
        "isTrial": 0
      }
    ],
    "categories": [
      {
        "categoryId": 23,
        "categoryName": "课程分类"
      }
    ],
    "purchased": false
  }

  courseDetail.value = testData
  const processedChapters = processChapterHierarchy(testData.chapters)
  chapterList.value = processedChapters

  // 默认展开第一个章节
  if (processedChapters.length > 0) {
    expandedChapters.value.push(processedChapters[0].id)
  }

  uni.setNavigationBarTitle({
    title: testData.title
  })

  console.log('测试数据加载完成:', courseDetail.value)
  console.log('处理后的章节:', chapterList.value)
  console.log('默认展开章节:', expandedChapters.value)
}

// 添加转发支持
const shareConfig = ref(null)

// 监听转发事件
uni.$on('triggerShare', (config) => {
  shareConfig.value = config
  // #ifdef MP-WEIXIN
  uni.showToast({
    title: '请点击右上角分享',
    icon: 'none'
  })
  // #endif
})

// 页面卸载时移除监听
onUnmounted(() => {
  uni.$off('triggerShare')
})
</script>

<script>
// 支持转发的页面配置
export default {
  onShareAppMessage() {
    const currentInstance = getCurrentInstance()
    const shareConfig = currentInstance?.ctx?.shareConfig

    if (shareConfig) {
      return {
        title: shareConfig.title,
        path: shareConfig.path,
        imageUrl: shareConfig.imageUrl
      }
    }

    // 默认分享配置
    const courseDetail = currentInstance?.ctx?.courseDetail
    if (courseDetail) {
      return {
        title: `推荐课程：${courseDetail.title}`,
        path: `pages/course/detail/index?id=${courseDetail.id}`,
        imageUrl: courseDetail.coverImage
      }
    }

    return {
      title: '熙桓心理课程',
      path: 'pages/index/index'
    }
  },

  onShareTimeline() {
    const currentInstance = getCurrentInstance()
    const shareConfig = currentInstance?.ctx?.shareConfig

    if (shareConfig) {
      return {
        title: shareConfig.title,
        query: '',
        imageUrl: shareConfig.imageUrl
      }
    }

    // 默认分享配置
    const courseDetail = currentInstance?.ctx?.courseDetail
    if (courseDetail) {
      return {
        title: `推荐课程：${courseDetail.title}`,
        query: `id=${courseDetail.id}`,
        imageUrl: courseDetail.coverImage
      }
    }

    return {
      title: '熙桓心理课程'
    }
  }
}
</script>

<style lang="scss" scoped>
.course-detail {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.course-header {
  position: relative;
  height: 400rpx;
  overflow: hidden;

  .cover-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.course-content {
  padding: 32rpx;
  background-color: #f5f5f5;

  .course-info-section {
    background: #fff;
    margin-bottom: 40rpx;

    .course-title {
      font-size: 40rpx;
      font-weight: 700;
      color: #000;
      margin-bottom: 23rpx;

    }

    .price-section {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .price-info {
        display: flex;
        align-items: baseline;
        gap: 8rpx;

        .free-tag {
          background: #4CAF50;
          color: white;
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
          font-size: 24rpx;
          font-weight: bold;
        }

        .price {
          font-size: 48rpx;
          font-weight: bold;
          color: #A04571;
        }

        .price-unit {
          font-size: 24rpx;
          color: #666;
        }
      }

      .sales-count {
        font-size: 24rpx;
        color: #ACA8AA;
      }
    }
  }

  .instructor-card {
    background: #fff;
    margin-bottom: 40rpx;
    display: flex;
    align-items: center;
    gap: 14rpx;

    .instructor-avatar {
      width: 98rpx;
      height: 98rpx;
      object-fit: cover;
    }

    .instructor-info {
      flex: 1;

      .instructor-name-title {
        display: flex;
        margin-bottom: 26rpx;
      }

      .instructor-name {
        font-size: 30rpx;
        font-weight: bold;
        color: #000;
        margin-right: 7rpx;
      }

      .instructor-title {
        font-size: 20rpx;
        color: #964070;
        background-color: #EFEAEE;
      }

      .instructor-desc {
        font-size: 24rpx;
        color: #8A8788;
      }
    }
  }

  .sticky-tabs {
    position: sticky;
    top: 0;
    z-index: 100;
    background: #fff;
    border-bottom: 1px solid #eee;

    .tab-scroll {
      white-space: nowrap;

      .tab-list {
        display: flex;
        padding: 0 30rpx;

        .tab-item {
          flex-shrink: 0;
          padding: 32rpx 24rpx;
          font-size: 30rpx;
          color: #666;
          position: relative;
          margin-right: 40rpx;

          &.active {
            color: #A04571;
            font-weight: 600;

            &::after {
              content: '';
              position: absolute;
              bottom: 0;
              left: 50%;
              transform: translateX(-50%);
              width: 60rpx;
              height: 4rpx;
              background-color: #A04571;
              border-radius: 2rpx;
            }
          }
        }
      }
    }
  }

  .content-area {
    flex: 1;
    padding-bottom: 160rpx;
  }

  .content-section {
    background: #fff;
    margin-bottom: 20rpx;
    padding: 30rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 24rpx;
    }

    .course-description {
      font-size: 28rpx;
      line-height: 1.6;
      color: #666;
      margin-bottom: 24rpx;
    }

    .course-features {
      .feature-item {
        display: flex;
        align-items: center;
        gap: 16rpx;
        margin-bottom: 16rpx;
        font-size: 28rpx;
        color: #666;
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 80rpx 0;
      color: #999;
      font-size: 28rpx;
      gap: 20rpx;
    }
  }

  .chapter-list {
    .chapter-item {
      margin-bottom: 20rpx;
      background-color: #fff;
      border-radius: 12rpx;
      overflow: hidden;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      border: 1rpx solid #f0f0f0;

      .chapter-header {
        display: flex;
        align-items: center;
        padding: 24rpx;

        .chapter-number {
          width: 48rpx;
          height: 48rpx;
          border-radius: 50%;
          background: #A04571;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24rpx;
          font-weight: 600;
          margin-right: 20rpx;
        }

        .chapter-info {
          flex: 1;

          .chapter-title {
            font-size: 30rpx;
            color: #333;
            font-weight: 600;
            margin-bottom: 8rpx;
          }

          .chapter-duration {
            font-size: 24rpx;
            color: #999;
          }
        }

        .chapter-status {
          width: 40rpx;
          height: 40rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .chapter-description {
        padding: 0 24rpx 24rpx;
        font-size: 26rpx;
        color: #666;
        line-height: 1.5;
      }
    }
  }

  .sub-chapters {
    background: linear-gradient(to bottom, #f8f9fa, #ffffff);

    .sub-chapter {
      display: flex;
      align-items: center;
      padding: 28rpx 32rpx;
      border-bottom: 1rpx solid #f0f0f0;
      position: relative;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        transform: translateX(8rpx);
      }

      &:last-child {
        border-bottom: none;
        border-radius: 0 0 20rpx 20rpx;
      }

      &::before {
        content: '';
        position: absolute;
        left: 32rpx;
        top: 0;
        width: 4rpx;
        height: 100%;
        background: linear-gradient(to bottom, #667eea, #764ba2);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover::before {
        opacity: 1;
      }

      .chapter-number {
        width: 56rpx;
        height: 56rpx;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20rpx;
        font-weight: 600;
        margin-right: 24rpx;
        margin-left: 20rpx;
        box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);

        &.sub {
          width: 64rpx;
          font-size: 18rpx;
        }
      }

      .chapter-info {
        flex: 1;

        .chapter-title {
          font-size: 30rpx;
          color: #333;
          margin-bottom: 12rpx;
          font-weight: 500;
          line-height: 1.4;
        }

        .chapter-meta {
          display: flex;
          gap: 16rpx;
          font-size: 24rpx;
          color: #666;
          flex-wrap: wrap;

          .trial-tag {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: #fff;
            padding: 6rpx 16rpx;
            border-radius: 16rpx;
            font-size: 20rpx;
            font-weight: 500;
            box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
          }

          .locked-tag {
            background: linear-gradient(135deg, #ff9800, #f57c00);
            color: #fff;
            padding: 6rpx 16rpx;
            border-radius: 16rpx;
            font-size: 20rpx;
            font-weight: 500;
            box-shadow: 0 2rpx 8rpx rgba(255, 152, 0, 0.3);
          }

          .video-tag {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: #fff;
            padding: 6rpx 16rpx;
            border-radius: 16rpx;
            font-size: 20rpx;
            font-weight: 500;
            box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.3);
          }
        }
      }

      .chapter-status {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(102, 126, 234, 0.1);
        border-radius: 50%;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(102, 126, 234, 0.2);
          transform: scale(1.1);
        }
      }
    }
  }

  // 评价区域样式
  .rating-summary {
    margin-bottom: 24rpx;

    .rating-score {
      display: flex;
      align-items: center;
      gap: 16rpx;

      .score-number {
        font-size: 48rpx;
        font-weight: bold;
        color: #A04571;
      }

      .stars-container {
        display: flex;
        gap: 4rpx;

        .star-icon {
          width: 32rpx;
          height: 32rpx;
        }
      }

      .total-reviews {
        font-size: 24rpx;
        color: #999;
      }
    }
  }

  .reviews-list {
    .review-item {
      margin-bottom: 20rpx;
    }
  }

  // 评价统计卡片
  .rating-summary-card {
    background-color: #fff;
    border-radius: 24rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

    .rating-overview {
      display: flex;
      justify-content: center;
      align-items: center;

      .rating-score {
        text-align: center;

        .score-number {
          display: block;
          font-size: 72rpx;
          font-weight: 700;
          color: #A04571;
          margin-bottom: 16rpx;
        }

        .stars-container {
          display: flex;
          justify-content: center;
          gap: 8rpx;
          margin-bottom: 16rpx;

          .star-icon {
            width: 32rpx;
            height: 32rpx;
          }
        }

        .total-reviews {
          font-size: 28rpx;
          color: #666;
        }
      }
    }
  }

  // 评价列表容器
  .reviews-container {
    .review-card {
      background-color: #fff;
      border-radius: 24rpx;
      padding: 32rpx;
      margin-bottom: 24rpx;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

      .review-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 24rpx;

        .user-section {
          display: flex;
          align-items: center;

          .user-avatar {
            width: 88rpx;
            height: 88rpx;
            border-radius: 50%;
            margin-right: 24rpx;
            border: 3rpx solid #f0f0f0;
          }

          .user-info {
            .user-name {
              display: block;
              font-size: 32rpx;
              font-weight: 600;
              color: #333;
              margin-bottom: 12rpx;
            }

            .review-rating {
              display: flex;
              gap: 6rpx;

              .rating-star {
                width: 28rpx;
                height: 28rpx;
              }
            }
          }
        }

        .review-date {
          font-size: 24rpx;
          color: #999;
          margin-top: 8rpx;
        }
      }

      .review-body {
        margin-bottom: 24rpx;

        .review-text {
          font-size: 30rpx;
          line-height: 1.6;
          color: #333;
          display: block;
        }
      }

      .course-tag {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16rpx 20rpx;
        background-color: #f8f9fa;
        border-radius: 12rpx;
        border-left: 4rpx solid #A04571;

        .course-label {
          font-size: 26rpx;
          color: #A04571;
          font-weight: 500;
        }

        .course-date {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }

  // 空状态样式
  .no-reviews {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80rpx 32rpx;
    background-color: #fff;
    border-radius: 24rpx;
    margin-bottom: 24rpx;

    .empty-icon {
      width: 120rpx;
      height: 120rpx;
      margin-bottom: 24rpx;
      opacity: 0.6;
    }

    .empty-text {
      font-size: 28rpx;
      color: #999;
    }
  }

  .bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 32rpx;
    background-color: #fff;
    border-top: 1px solid #eee;

    .price-info {
      display: flex;
      align-items: baseline;

      .price-symbol {
        font-size: 24rpx;
        color: #ff6b35;
      }

      .price-value {
        font-size: 40rpx;
        font-weight: 600;
        color: #ff6b35;
      }

      .original-price {
        font-size: 24rpx;
        color: #999;
        text-decoration: line-through;
        margin-left: 12rpx;
      }
    }

    .action-buttons {

      .buy-button,
      .study-button {
        padding: 20rpx 48rpx;
        border-radius: 40rpx;
        font-size: 30rpx;
        font-weight: 600;
        border: none;
      }

      .buy-button {
        background-color: #ff6b35;
        color: #fff;
      }

      .study-button {
        background-color: #4CAF50;
        color: #fff;
      }
    }
  }
}
</style>
