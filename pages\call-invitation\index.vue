<template>
  <view class="call-invitation-container">
    <!-- 背景 -->
    <view class="background">
      <image src="/static/bg/call-bg.jpg" mode="aspectFill" />
    </view>
    
    <!-- 用户信息 -->
    <view class="user-info">
      <image class="avatar" :src="callerAvatar" mode="aspectFill" />
      <text class="name">{{ callerName }}</text>
      <text class="subtitle">{{ isIncoming ? '邀请您进行视频咨询' : '正在呼叫...' }}</text>
    </view>
    
    <!-- 通话类型 -->
    <view class="call-type">
      <image :src="callType === 'video' ? '/static/icon/video-call.png' : '/static/icon/voice-call.png'" />
      <text>{{ callType === 'video' ? '视频通话' : '语音通话' }}</text>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <view v-if="isIncoming" class="incoming-buttons">
        <!-- 拒绝按钮 -->
        <view class="action-btn reject-btn" @click="rejectCall">
          <image src="/static/icon/reject.png" />
          <text>拒绝</text>
        </view>
        
        <!-- 接听按钮 -->
        <view class="action-btn accept-btn" @click="acceptCall">
          <image src="/static/icon/accept.png" />
          <text>接听</text>
        </view>
      </view>
      
      <view v-else class="outgoing-buttons">
        <!-- 取消按钮 -->
        <view class="action-btn cancel-btn" @click="cancelCall">
          <image src="/static/icon/hangup.png" />
          <text>取消</text>
        </view>
      </view>
    </view>
    
    <!-- 状态提示 -->
    <view class="status-text">
      <text>{{ statusText }}</text>
    </view>
  </view>
</template>

<script>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useUserStore } from '@/stores/user'

export default {
  name: 'CallInvitation',
  setup() {
    const userStore = useUserStore()
    
    // 响应式数据
    const isIncoming = ref(true) // 是否是来电
    const callType = ref('video') // video | voice
    const callerName = ref('')
    const callerAvatar = ref('')
    const channelId = ref('')
    const appointmentId = ref('')
    const callStatus = ref('ringing') // ringing, connecting, connected, rejected, cancelled
    
    // 计算属性
    const statusText = computed(() => {
      switch (callStatus.value) {
        case 'ringing': return isIncoming.value ? '来电中...' : '呼叫中...'
        case 'connecting': return '连接中...'
        case 'connected': return '已接通'
        case 'rejected': return '已拒绝'
        case 'cancelled': return '已取消'
        default: return ''
      }
    })
    
    // 页面加载
    onMounted(() => {
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const options = currentPage.options
      
      isIncoming.value = options.type === 'incoming'
      callType.value = options.callType || 'video'
      callerName.value = options.callerName || '对方'
      callerAvatar.value = options.callerAvatar || '/static/icon/default-avatar.png'
      channelId.value = options.channelId
      appointmentId.value = options.appointmentId
      
      // 如果是拨出电话，自动开始呼叫
      if (!isIncoming.value) {
        startOutgoingCall()
      }
      
      // 播放铃声
      playRingtone()
    })
    
    // 页面卸载
    onUnmounted(() => {
      stopRingtone()
    })
    
    // 开始拨出电话
    const startOutgoingCall = async () => {
      try {
        callStatus.value = 'ringing'
        
        // 发送呼叫请求到服务端
        const res = await uni.request({
          url: '/api/rtc/start-call',
          method: 'POST',
          data: {
            appointmentId: appointmentId.value,
            channelId: channelId.value,
            callType: callType.value
          }
        })
        
        if (res.data.code === 200) {
          // 等待对方响应
          waitForResponse()
        } else {
          throw new Error('发起通话失败')
        }
      } catch (error) {
        console.error('发起通话失败:', error)
        uni.showToast({
          title: '发起通话失败',
          icon: 'none'
        })
        uni.navigateBack()
      }
    }
    
    // 等待对方响应
    const waitForResponse = () => {
      // 这里可以通过WebSocket或轮询等方式等待对方响应
      // 模拟等待30秒
      setTimeout(() => {
        if (callStatus.value === 'ringing') {
          callStatus.value = 'cancelled'
          uni.showToast({
            title: '对方未接听',
            icon: 'none'
          })
          setTimeout(() => {
            uni.navigateBack()
          }, 2000)
        }
      }, 30000)
    }
    
    // 接听电话
    const acceptCall = async () => {
      try {
        callStatus.value = 'connecting'
        
        // 发送接听请求到服务端
        const res = await uni.request({
          url: '/api/rtc/accept-call',
          method: 'POST',
          data: {
            channelId: channelId.value,
            userId: userStore.userId
          }
        })
        
        if (res.data.code === 200) {
          callStatus.value = 'connected'
          stopRingtone()
          
          // 跳转到通话页面
          uni.redirectTo({
            url: `/pages/video-call/index?channelId=${channelId.value}&appointmentId=${appointmentId.value}&remoteUserId=${res.data.data.remoteUserId}&remoteUserName=${callerName.value}&remoteUserAvatar=${callerAvatar.value}`
          })
        } else {
          throw new Error('接听失败')
        }
      } catch (error) {
        console.error('接听失败:', error)
        uni.showToast({
          title: '接听失败',
          icon: 'none'
        })
      }
    }
    
    // 拒绝电话
    const rejectCall = async () => {
      try {
        callStatus.value = 'rejected'
        
        // 发送拒绝请求到服务端
        await uni.request({
          url: '/api/rtc/reject-call',
          method: 'POST',
          data: {
            channelId: channelId.value,
            userId: userStore.userId
          }
        })
        
        stopRingtone()
        
        uni.showToast({
          title: '已拒绝',
          icon: 'none'
        })
        
        setTimeout(() => {
          uni.navigateBack()
        }, 1000)
      } catch (error) {
        console.error('拒绝失败:', error)
        uni.navigateBack()
      }
    }
    
    // 取消电话
    const cancelCall = async () => {
      try {
        callStatus.value = 'cancelled'
        
        // 发送取消请求到服务端
        await uni.request({
          url: '/api/rtc/cancel-call',
          method: 'POST',
          data: {
            channelId: channelId.value,
            userId: userStore.userId
          }
        })
        
        stopRingtone()
        
        uni.showToast({
          title: '已取消',
          icon: 'none'
        })
        
        setTimeout(() => {
          uni.navigateBack()
        }, 1000)
      } catch (error) {
        console.error('取消失败:', error)
        uni.navigateBack()
      }
    }
    
    // 播放铃声
    const playRingtone = () => {
      // 小程序播放铃声
      uni.playBackgroundAudio({
        dataUrl: '/static/audio/ringtone.mp3',
        title: '通话铃声'
      })
    }
    
    // 停止铃声
    const stopRingtone = () => {
      uni.stopBackgroundAudio()
    }
    
    return {
      isIncoming,
      callType,
      callerName,
      callerAvatar,
      statusText,
      acceptCall,
      rejectCall,
      cancelCall
    }
  }
}
</script>

<style scoped lang="scss">
.call-invitation-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 100rpx 40rpx;
  box-sizing: border-box;
}

.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  
  image {
    width: 100%;
    height: 100%;
    filter: blur(10rpx);
  }
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
  }
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 200rpx;
  
  .avatar {
    width: 300rpx;
    height: 300rpx;
    border-radius: 150rpx;
    border: 8rpx solid #fff;
    margin-bottom: 40rpx;
  }
  
  .name {
    color: #fff;
    font-size: 48rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
  }
  
  .subtitle {
    color: #ccc;
    font-size: 32rpx;
  }
}

.call-type {
  display: flex;
  align-items: center;
  padding: 20rpx 40rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50rpx;
  
  image {
    width: 40rpx;
    height: 40rpx;
    margin-right: 20rpx;
  }
  
  text {
    color: #fff;
    font-size: 28rpx;
  }
}

.action-buttons {
  .incoming-buttons {
    display: flex;
    justify-content: space-between;
    width: 400rpx;
  }
  
  .outgoing-buttons {
    display: flex;
    justify-content: center;
  }
  
  .action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 160rpx;
    height: 160rpx;
    border-radius: 80rpx;
    
    &.accept-btn {
      background-color: #2ed573;
    }
    
    &.reject-btn, &.cancel-btn {
      background-color: #ff4757;
    }
    
    image {
      width: 60rpx;
      height: 60rpx;
      margin-bottom: 10rpx;
    }
    
    text {
      color: #fff;
      font-size: 24rpx;
    }
  }
}

.status-text {
  text {
    color: #ccc;
    font-size: 28rpx;
  }
}
</style>
