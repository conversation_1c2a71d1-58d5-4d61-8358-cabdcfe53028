<template>
  <view class="my-consultations">
    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stat-item">
        <view class="stat-number">{{ statistics.totalConsultations || 0 }}</view>
        <view class="stat-label">总咨询次数</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{ statistics.completedConsultations || 0 }}</view>
        <view class="stat-label">已完成</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{ formatDuration(statistics.totalDuration || 0) }}</view>
        <view class="stat-label">总时长</view>
      </view>
    </view>

    <!-- 筛选标签 -->
    <view class="filter-tabs">
      <view 
        v-for="(tab, index) in filterTabs" 
        :key="index"
        :class="['tab-item', { active: currentFilter === index }]"
        @click="switchFilter(index)"
      >
        {{ tab.name }}
      </view>
    </view>

    <!-- 咨询列表 -->
    <scroll-view 
      class="consultation-list" 
      scroll-y 
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view v-if="filteredList.length === 0" class="empty-state">
        <image src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/empty-consultation.png" mode="aspectFit"></image>
        <text>暂无咨询记录</text>
        <button class="browse-btn" @click="goToBrowse">寻找咨询师</button>
      </view>

      <view v-else class="consultation-items">
        <!-- 订单列表 -->
        <view 
          v-if="currentFilter === 0"
          v-for="order in orderList" 
          :key="order.id"
          class="consultation-item"
          @click="goToOrderDetail(order.id)"
        >
          <view class="consultation-header">
            <view class="consultant-info">
              <image :src="order.consultantAvatar || defaultAvatar" class="consultant-avatar"></image>
              <view class="consultant-details">
                <view class="consultant-name">{{ order.consultantName }}</view>
                <view class="consultation-type">{{ getConsultationTypeName(order.consultationType) }}</view>
              </view>
            </view>
            <view class="order-status" :class="getStatusClass(order.status)">
              {{ getStatusText(order.status) }}
            </view>
          </view>
          
          <view class="consultation-content">
            <view class="appointment-time">
              <uni-icons type="time" size="16" color="#666"></uni-icons>
              <text>{{ formatDateTime(order.appointmentTime) }}</text>
            </view>
            <view class="duration">
              <uni-icons type="clock" size="16" color="#666"></uni-icons>
              <text>{{ order.duration }}分钟</text>
            </view>
            <view class="amount">
              <text class="amount-label">费用：</text>
              <text class="amount-value">¥{{ order.paymentAmount }}</text>
            </view>
          </view>
          
          <view class="consultation-actions">
            <!-- 待支付状态 -->
            <template v-if="order.status === 'pending'">
              <button class="action-btn cancel-btn" @click.stop="cancelOrder(order.id)">取消预约</button>
              <button class="action-btn pay-btn" @click.stop="payOrder(order)">立即支付</button>
            </template>

            <!-- 待咨询或咨询中状态 -->
            <template v-if="order.status === 'confirmed' || order.status === 'in_progress'">
              <button class="action-btn video-btn" @click.stop="startVideoCall(order)">视频通话</button>
            </template>
          </view>
        </view>

        <!-- 咨询记录列表 -->
        <view 
          v-if="currentFilter === 1"
          v-for="record in recordList" 
          :key="record.id"
          class="consultation-item"
          @click="goToRecordDetail(record.id)"
        >
          <view class="consultation-header">
            <view class="consultant-info">
              <image :src="record.consultantAvatar || defaultAvatar" class="consultant-avatar"></image>
              <view class="consultant-details">
                <view class="consultant-name">{{ record.consultantName }}</view>
                <view class="consultation-type">{{ getConsultationTypeName(record.consultationType) }}</view>
              </view>
            </view>
            <view class="record-status" :class="getRecordStatusClass(record.status)">
              {{ getRecordStatusText(record.status) }}
            </view>
          </view>
          
          <view class="consultation-content">
            <view class="consultation-time">
              <uni-icons type="time" size="16" color="#666"></uni-icons>
              <text>{{ formatDateTime(record.startTime) }} - {{ formatTime(record.endTime) }}</text>
            </view>
            <view class="duration">
              <uni-icons type="clock" size="16" color="#666"></uni-icons>
              <text>{{ record.actualDuration || record.duration }}分钟</text>
            </view>
          </view>
          
          <view class="consultation-actions" v-if="record.status === 'completed' && !record.hasReviewed">
            <button class="action-btn review-btn" @click.stop="goToReview(record.id)">评价咨询</button>
          </view>
        </view>

        <!-- 评价列表 -->
        <view 
          v-if="currentFilter === 2"
          v-for="review in reviewList" 
          :key="review.id"
          class="consultation-item review-item"
        >
          <view class="consultation-header">
            <view class="consultant-info">
              <image :src="review.consultantAvatar || defaultAvatar" class="consultant-avatar"></image>
              <view class="consultant-details">
                <view class="consultant-name">{{ review.consultantName }}</view>
                <view class="review-date">{{ formatDate(review.reviewTime) }}</view>
              </view>
            </view>
            <uni-rate :value="review.rating" readonly size="16"></uni-rate>
          </view>
          
          <view class="review-content">
            <text>{{ review.content }}</text>
          </view>
          
          <view class="review-tags" v-if="review.tags">
            <view v-for="tag in getReviewTags(review.tags)" :key="tag" class="tag-item">
              {{ tag }}
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { 
  getUserOrders, 
  getUserRecords, 
  getUserReviews, 
  getUserStatistics,
  cancelOrder as cancelOrderApi
} from '@/api/consultation'
import { useUserStore } from '@/stores/user'

// 响应式数据
const orderList = ref([])
const recordList = ref([])
const reviewList = ref([])
const statistics = ref({})
const refreshing = ref(false)
const filterTabs = ref([
  { name: '我的预约', value: 'orders' },
  { name: '咨询记录', value: 'records' },
  { name: '我的评价', value: 'reviews' }
])
const currentFilter = ref(0)

// 默认头像
const defaultAvatar = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-avatar.png'

const userStore = useUserStore()

// 计算属性
const filteredList = computed(() => {
  const filter = filterTabs.value[currentFilter.value].value
  
  if (filter === 'orders') {
    return orderList.value
  } else if (filter === 'records') {
    return recordList.value
  } else if (filter === 'reviews') {
    return reviewList.value
  }
  
  return []
})

// 方法
const switchFilter = (index) => {
  currentFilter.value = index
  
  const filter = filterTabs.value[index].value
  if (filter === 'orders' && orderList.value.length === 0) {
    loadOrderList()
  } else if (filter === 'records' && recordList.value.length === 0) {
    loadRecordList()
  } else if (filter === 'reviews' && reviewList.value.length === 0) {
    loadReviewList()
  }
}

const loadOrderList = async () => {
  if (!userStore.isLoggedIn) {
    uni.navigateTo({
      url: '/pages/login/login'
    })
    return
  }

  try {
    const res = await getUserOrders()
    if (res.code === 200) {
      orderList.value = res.data || []
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
  }
}

const loadRecordList = async () => {
  if (!userStore.isLoggedIn) return

  try {
    const res = await getUserRecords()
    if (res.code === 200) {
      recordList.value = res.data || []
    }
  } catch (error) {
    console.error('获取咨询记录失败:', error)
  }
}

const loadReviewList = async () => {
  if (!userStore.isLoggedIn) return

  try {
    const res = await getUserReviews()
    if (res.code === 200) {
      reviewList.value = res.data || []
    }
  } catch (error) {
    console.error('获取评价列表失败:', error)
  }
}

const loadStatistics = async () => {
  if (!userStore.isLoggedIn) return

  try {
    const res = await getUserStatistics()
    if (res.code === 200) {
      statistics.value = res.data || {}
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

const onRefresh = async () => {
  refreshing.value = true
  
  await Promise.all([
    loadOrderList(),
    loadRecordList(),
    loadReviewList(),
    loadStatistics()
  ])
  
  refreshing.value = false
}

const cancelOrder = async (orderId) => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消这个预约吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          const result = await cancelOrderApi(orderId, '用户主动取消')
          if (result.code === 200) {
            uni.showToast({
              title: '取消成功',
              icon: 'success'
            })
            loadOrderList()
          } else {
            uni.showToast({
              title: result.msg || '取消失败',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('取消订单失败:', error)
          uni.showToast({
            title: '网络请求失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

const payOrder = (order) => {
  // TODO: 实现支付逻辑
  console.log('支付订单:', order)
}

// 发起视频通话
const startVideoCall = async (order) => {
  try {
    // 创建通话房间
    const res = await uni.request({
      url: '/api/rtc/create-room',
      method: 'POST',
      data: {
        appointmentId: order.id,
        consultantId: order.consultantId,
        clientId: userStore.userId,
        callType: 'video'
      }
    })

    if (res.data.code === 200) {
      const { channelId } = res.data.data

      // 跳转到通话邀请页面
      uni.navigateTo({
        url: `/pages/call-invitation/index?type=outgoing&channelId=${channelId}&appointmentId=${order.id}&callType=video&callerName=${order.consultantName}&callerAvatar=${order.consultantAvatar}`
      })
    } else {
      throw new Error(res.data.msg || '创建通话房间失败')
    }
  } catch (error) {
    console.error('发起视频通话失败:', error)
    uni.showToast({
      title: '发起通话失败',
      icon: 'none'
    })
  }
}

const goToOrderDetail = (orderId) => {
  uni.navigateTo({
    url: `/pages/consultation/order-detail/index?id=${orderId}`
  })
}

const goToRecordDetail = (recordId) => {
  uni.navigateTo({
    url: `/pages/consultation/record-detail/index?id=${recordId}`
  })
}

const goToReview = (recordId) => {
  uni.navigateTo({
    url: `/pages/consultation/review/index?recordId=${recordId}`
  })
}

const goToBrowse = () => {
  uni.switchTab({
    url: '/pages/classification/index'
  })
}

// 工具方法
const formatDuration = (minutes) => {
  if (minutes < 60) {
    return `${minutes}分钟`
  } else {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours}小时${mins > 0 ? mins + '分钟' : ''}`
  }
}

const formatDateTime = (dateStr) => {
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}-${date.getDate()} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

const formatTime = (dateStr) => {
  const date = new Date(dateStr)
  return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

const formatDate = (dateStr) => {
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

const getConsultationTypeName = (type) => {
  const types = {
    'psychological': '心理咨询',
    'emotional': '情感咨询',
    'career': '职场咨询',
    'family': '家庭咨询'
  }
  return types[type] || type
}

const getStatusText = (status) => {
  const statusMap = {
    'pending': '待支付',
    'paid': '已支付',
    'confirmed': '已确认',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

const getStatusClass = (status) => {
  return `status-${status}`
}

const getRecordStatusText = (status) => {
  const statusMap = {
    'scheduled': '已预约',
    'in_progress': '进行中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

const getRecordStatusClass = (status) => {
  return `record-status-${status}`
}

const getReviewTags = (tags) => {
  try {
    return JSON.parse(tags)
  } catch (e) {
    return []
  }
}

// 生命周期
onLoad(() => {
  Promise.all([
    loadOrderList(),
    loadStatistics()
  ])
})
</script>

<style lang="scss" scoped>
.my-consultations {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.stats-section {
  display: flex;
  background-color: #fff;
  padding: 32rpx;
  margin-bottom: 16rpx;

  .stat-item {
    flex: 1;
    text-align: center;

    .stat-number {
      font-size: 48rpx;
      font-weight: 600;
      color: #ff6b35;
      margin-bottom: 8rpx;
    }

    .stat-label {
      font-size: 26rpx;
      color: #666;
    }
  }
}

.filter-tabs {
  display: flex;
  background-color: #fff;
  padding: 24rpx 32rpx;
  margin-bottom: 16rpx;

  .tab-item {
    flex: 1;
    text-align: center;
    padding: 16rpx 0;
    font-size: 28rpx;
    color: #666;
    position: relative;

    &.active {
      color: #ff6b35;
      font-weight: 600;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 4rpx;
        background-color: #ff6b35;
        border-radius: 2rpx;
      }
    }
  }
}

.consultation-list {
  flex: 1;
  padding: 0 32rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 32rpx;
  text-align: center;

  image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 32rpx;
  }

  text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
  }

  .browse-btn {
    padding: 20rpx 48rpx;
    background-color: #ff6b35;
    color: #fff;
    border: none;
    border-radius: 40rpx;
    font-size: 28rpx;
  }
}

.consultation-items {
  .consultation-item {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 16rpx;

    .consultation-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20rpx;

      .consultant-info {
        display: flex;
        align-items: center;

        .consultant-avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          margin-right: 20rpx;
        }

        .consultant-details {
          .consultant-name {
            font-size: 30rpx;
            font-weight: 600;
            color: #333;
            margin-bottom: 8rpx;
          }

          .consultation-type, .review-date {
            font-size: 24rpx;
            color: #999;
          }
        }
      }

      .order-status, .record-status {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;

        &.status-pending {
          background-color: #fff3e0;
          color: #ff9800;
        }

        &.status-paid, &.status-confirmed {
          background-color: #e3f2fd;
          color: #2196f3;
        }

        &.status-completed {
          background-color: #e8f5e8;
          color: #4caf50;
        }

        &.status-cancelled {
          background-color: #ffebee;
          color: #f44336;
        }

        &.record-status-scheduled {
          background-color: #e3f2fd;
          color: #2196f3;
        }

        &.record-status-in_progress {
          background-color: #fff3e0;
          color: #ff9800;
        }

        &.record-status-completed {
          background-color: #e8f5e8;
          color: #4caf50;
        }

        &.record-status-cancelled {
          background-color: #ffebee;
          color: #f44336;
        }
      }
    }

    .consultation-content {
      margin-bottom: 20rpx;

      > view {
        display: flex;
        align-items: center;
        gap: 12rpx;
        margin-bottom: 12rpx;
        font-size: 26rpx;
        color: #666;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .amount {
        .amount-label {
          color: #666;
        }

        .amount-value {
          color: #ff6b35;
          font-weight: 600;
        }
      }
    }

    .consultation-actions {
      display: flex;
      gap: 16rpx;
      justify-content: flex-end;

      .action-btn {
        padding: 12rpx 24rpx;
        border: none;
        border-radius: 20rpx;
        font-size: 24rpx;

        &.cancel-btn {
          background-color: #f5f5f5;
          color: #666;
        }

        &.pay-btn {
          background-color: #ff6b35;
          color: #fff;
        }

        &.video-btn {
          background-color: #0ea5e9;
          color: #fff;

          &::before {
            content: '📹';
            margin-right: 8rpx;
          }
        }

        &.review-btn {
          background-color: #4caf50;
          color: #fff;
        }
      }
    }

    &.review-item {
      .review-content {
        margin-bottom: 16rpx;

        text {
          font-size: 28rpx;
          line-height: 1.6;
          color: #333;
        }
      }

      .review-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 12rpx;

        .tag-item {
          padding: 8rpx 16rpx;
          background-color: #f0f0f0;
          color: #666;
          border-radius: 20rpx;
          font-size: 24rpx;
        }
      }
    }
  }
}
</style>
