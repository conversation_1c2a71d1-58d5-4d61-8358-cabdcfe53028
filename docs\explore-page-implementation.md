# 探索页面实现完善

## 功能概述

探索页面是测评和冥想的统一入口，包含以下核心功能：
1. **双Tab切换**：测评 / 冥想
2. **二级分类筛选**：每个Tab下都有对应的分类
3. **轮播图展示**：展示热门内容
4. **列表展示**：支持分页加载
5. **搜索功能**：跳转到搜索页面

## 当前实现状态

### ✅ **已实现功能**

#### 🎯 **基础架构**
- **Tab切换**：测评/冥想双Tab结构
- **分类筛选**：横向滚动的分类选择器
- **搜索入口**：自定义导航栏搜索框
- **轮播图**：基于列表前3项的轮播展示
- **列表展示**：卡片式列表布局

#### 📊 **测评模块**
- **数据源**：`getCategories()` API
- **分类加载**：自动加载测评分类和对应的测评列表
- **数据结构**：
  ```javascript
  assessmentCategories: [
    {
      categoryId: 1,
      categoryName: "情绪测评",
      count: 15,
      assessments: [...]
    }
  ]
  ```
- **筛选逻辑**：支持"全部"和具体分类筛选

#### 🧘 **冥想模块**
- **数据源**：`getMeditationCategories()` + `getExploreMeditationList()` API
- **分类加载**：独立加载冥想分类
- **分页支持**：支持上拉加载更多
- **分类筛选**：根据选择的分类ID筛选内容

### 🔧 **核心技术实现**

#### 📱 **自定义导航栏**
```javascript
// 适配不同平台的导航栏高度计算
const initNavigationBar = () => {
  // 获取系统信息和胶囊按钮位置
  // 计算导航栏高度和搜索框位置
}
```

#### 🔄 **Tab切换逻辑**
```javascript
const switchTab = (index) => {
  currentTab.value = index
  currentCategory.value = -1  // 重置分类选择
  page.value = 1             // 重置分页
  loadData()                 // 重新加载数据
}
```

#### 🏷️ **分类筛选逻辑**
```javascript
const switchCategory = (index) => {
  currentCategory.value = index
  page.value = 1
  loadData()
}

// 计算当前显示的分类列表
const categories = computed(() => {
  return currentTab.value === 0 ? assessmentCategories.value : meditationCategories.value
})
```

#### 📋 **列表数据计算**
```javascript
const currentList = computed(() => {
  if (currentTab.value === 0) {
    // 测评：从分类数据中提取
    if (currentCategory.value === -1) {
      return assessmentList.value  // 全部测评
    } else {
      const category = assessmentCategories.value[currentCategory.value]
      return category ? category.assessments : []
    }
  } else {
    // 冥想：直接使用筛选后的列表
    return meditationList.value
  }
})
```

### 🎨 **UI设计特点**

#### 🔍 **搜索栏设计**
- **自适应布局**：根据胶囊按钮位置自动调整
- **平台兼容**：支持微信小程序、H5、APP
- **交互反馈**：点击跳转到搜索页面

#### 🏷️ **分类标签设计**
- **横向滚动**：支持多个分类的横向滚动
- **活跃状态**：当前选中分类高亮显示
- **数量显示**：显示每个分类下的内容数量
- **全部选项**：提供"全部"选项查看所有内容

#### 🎠 **轮播图设计**
- **动态内容**：基于当前Tab的前3项内容
- **自动播放**：3秒间隔自动切换
- **指示器**：底部圆点指示器
- **点击跳转**：点击轮播图跳转到详情页

#### 📋 **列表卡片设计**
- **图文布局**：左图右文的卡片式布局
- **信息层次**：标题、描述、元信息的清晰层次
- **状态标识**：题目数量、时长、价格等信息
- **交互反馈**：点击跳转到对应详情页

### 🔄 **数据流设计**

#### 📊 **测评数据流**
1. **加载分类**：`getCategories()` → 获取分类和测评数据
2. **数据处理**：提取分类列表和合并所有测评
3. **分类筛选**：根据选择的分类显示对应测评
4. **详情跳转**：`/pages/evaluation/detail/index?id=${item.id}`

#### 🧘 **冥想数据流**
1. **加载分类**：`getMeditationCategories()` → 获取冥想分类
2. **加载列表**：`getExploreMeditationList()` → 根据分类筛选
3. **分页加载**：支持上拉加载更多
4. **详情跳转**：`/pages/meditation/index?id=${item.id}`

### 🎯 **用户体验优化**

#### ⚡ **性能优化**
- **懒加载**：分类数据按需加载
- **缓存机制**：避免重复请求相同数据
- **分页加载**：冥想列表支持分页，避免一次性加载过多数据

#### 🎨 **视觉优化**
- **统一风格**：与其他页面保持一致的设计语言
- **响应式布局**：适配不同屏幕尺寸
- **加载状态**：提供加载中和加载完成的视觉反馈

#### 🚀 **交互优化**
- **流畅切换**：Tab和分类切换动画流畅
- **直观反馈**：当前选中状态清晰可见
- **便捷操作**：一键搜索、快速分类筛选

## API接口使用

### 📊 **测评相关**
```javascript
// 获取测评分类和数据
import { getCategories } from '@/api/evaluation'

// 返回数据结构
{
  categories: [
    {
      categoryId: 1,
      categoryName: "情绪测评",
      count: 15,
      assessments: [...]
    }
  ],
  totalAssessments: 50,
  totalCategories: 5
}
```

### 🧘 **冥想相关**
```javascript
// 获取冥想分类
import { getMeditationCategories } from '@/api/explore'

// 获取冥想列表
import { getExploreMeditationList } from '@/api/explore'

// 请求参数
{
  keyword: '',
  categoryId: 1,  // 可选，分类筛选
  page: 1,
  pageSize: 10
}
```

## 页面路由

### 🔗 **跳转路径**
- **测评详情**：`/pages/evaluation/detail/index?id=${item.id}`
- **冥想详情**：`/pages/meditation/index?id=${item.id}`
- **搜索页面**：`/pages/search/search?type=${currentTab.value}`

### 📱 **导航结构**
- **底部TabBar**：使用自定义TabBar组件 `cc-myTabbar`
- **页面标识**：`:tabBarShow="3"` 标识当前为探索页面

## 技术特点

### 🎯 **组件化设计**
- **uni-icons**：搜索图标
- **uni-load-more**：加载状态组件
- **cc-myTabbar**：自定义底部导航
- **scroll-view**：分类横向滚动和内容纵向滚动

### 📱 **平台适配**
- **条件编译**：`#ifdef MP-WEIXIN` 微信小程序特殊处理
- **系统信息获取**：适配不同平台的API差异
- **导航栏计算**：精确计算各平台的导航栏高度

### 🔄 **状态管理**
- **响应式数据**：使用Vue 3 Composition API
- **计算属性**：动态计算当前显示内容
- **生命周期**：合理利用onLoad和onReady钩子

现在探索页面已经完整实现了测评和冥想的列表展示以及二级分类筛选功能！
