# 课程详情页面修复

## 修复的问题

### 1. 课程评价未在进入时请求
**问题**: 点击进入课程详情时，没有立即请求课程评价数据，需要手动点击评价tab才会加载

**解决方案**: 
- 在 `onLoad` 生命周期中添加 `loadReviewList()` 调用
- 确保进入页面时就开始加载评价数据

```javascript
onLoad((options) => {
  courseId.value = options.id
  
  if (options.id) {
    loadCourseDetail()
    // 立即加载评价数据
    loadReviewList()
  }
})
```

### 2. 缺少相关课程tab
**问题**: 课程详情的tab结构与咨询师详情不一致，缺少相关课程推荐功能

**解决方案**:
1. **添加相关课程tab**:
   ```javascript
   const tabs = [
     { name: '课程介绍', id: 'section-0' },
     { name: '课程目录', id: 'section-1' },
     { name: '用户评价', id: 'section-2' },
     { name: '相关课程', id: 'section-3' }  // 新增
   ]
   ```

2. **添加相关课程API接口**:
   ```javascript
   // 获取相关课程推荐
   export function getRecommendedCourses(courseId, limit = 5) {
     return request({
       url: `/miniapp/course/recommended/${courseId}`,
       method: 'get',
       params: { limit }
     })
   }
   ```

3. **添加相关课程区域HTML**:
   ```vue
   <!-- 相关课程区域 -->
   <view id="section-3" class="content-section">
     <view class="section-title">相关课程</view>
     
     <view v-if="relatedCourses.length > 0" class="related-courses-list">
       <view v-for="course in relatedCourses" :key="course.id" 
             class="course-item" @click="goToCourseDetail(course.id)">
         <image :src="course.coverImage || defaultCover" 
                mode="aspectFill" class="course-cover"></image>
         <view class="course-info">
           <view class="course-title">{{ course.title }}</view>
           <view class="course-instructor">{{ course.instructor?.name || '讲师' }}</view>
           <view class="course-meta">
             <view class="course-price" v-if="course.isFree !== 1">
               <text class="price-symbol">¥</text>
               <text class="price-value">{{ course.price || 0 }}</text>
             </view>
             <view class="free-tag" v-else>
               <text>免费</text>
             </view>
             <view class="course-sales">{{ course.salesCount || 0 }}人学习</view>
           </view>
         </view>
       </view>
     </view>
   </view>
   ```

4. **添加相关课程加载逻辑**:
   ```javascript
   const loadRelatedCourses = async () => {
     try {
       const res = await getRecommendedCourses(courseId.value, 5)
       if (res.code === 200) {
         relatedCourses.value = res.data || []
       }
     } catch (error) {
       console.error('获取相关课程失败:', error)
     }
   }
   
   const goToCourseDetail = (id) => {
     uni.navigateTo({
       url: `/pages/course/detail/index?id=${id}`
     })
   }
   ```

5. **在tab切换时懒加载相关课程**:
   ```javascript
   // 在 scrollToSection 方法中添加
   if (index === 3 && relatedCourses.value.length === 0) {
     loadRelatedCourses()
   }
   ```

## 修复效果

### ✅ 评价数据立即加载
- 进入课程详情页面时立即开始加载评价数据
- 用户无需等待或手动点击评价tab
- 提升用户体验和页面加载效率

### ✅ 完整的tab结构
- 课程详情现在包含4个tab：课程介绍、课程目录、用户评价、相关课程
- 与咨询师详情页面的tab结构保持一致
- 提供完整的课程浏览体验

### ✅ 相关课程推荐
- 类似测评详情的相关测评功能
- 帮助用户发现更多相关课程
- 提高课程的交叉销售机会
- 增强用户粘性和平台使用时长

### ✅ 懒加载优化
- 相关课程数据在用户点击对应tab时才加载
- 避免不必要的网络请求
- 提升页面初始加载速度

## 技术要点

1. **数据管理**: 添加 `relatedCourses` 响应式数据
2. **API集成**: 新增 `getRecommendedCourses` 接口
3. **生命周期优化**: 在 `onLoad` 时立即加载评价数据
4. **懒加载策略**: tab切换时按需加载数据
5. **导航功能**: 支持从相关课程跳转到其他课程详情
6. **样式设计**: 相关课程列表采用卡片式布局，包含封面、标题、讲师、价格等信息

这个修复确保了课程详情页面的功能完整性和用户体验的一致性！
