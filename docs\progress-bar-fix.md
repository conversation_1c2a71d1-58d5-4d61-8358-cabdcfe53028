# 进度条拖动问题修复

## 问题描述

在冥想播放器中拖动进度条时，进度条会出现来回跳动的现象：
- 拖动时进度条突然跳到左边
- 然后又回到拖动的位置
- 造成用户体验不佳

## 问题原因

1. **时间更新冲突**: 拖动时直接修改 `currentTime.value`，但音频播放器的 `onTimeUpdate` 事件也在同时更新这个值，导致冲突
2. **缺少拖动状态隔离**: 没有区分拖动状态和正常播放状态的时间显示

## 解决方案

### 1. 添加拖动状态检查

在音频播放器的时间更新回调中添加拖动状态检查：

```javascript
audioPlayer.value.onTimeUpdate(() => {
  // 拖动时不更新时间，避免冲突
  if (!isDragging.value) {
    currentTime.value = audioPlayer.value.currentTime || 0
    duration.value = audioPlayer.value.duration || 0
  }
})
```

### 2. 引入临时拖动时间

添加 `dragCurrentTime` 变量来存储拖动时的临时时间：

```javascript
const dragCurrentTime = ref(0) // 拖动时的临时时间
```

### 3. 修改进度条计算

让进度条在拖动时使用临时时间：

```javascript
const progressPercent = computed(() => {
  if (duration.value === 0) return 0
  // 拖动时使用拖动的临时时间，否则使用实际播放时间
  const timeToUse = isDragging.value ? dragCurrentTime.value : currentTime.value
  return (timeToUse / duration.value) * 100
})
```

### 4. 优化拖动事件处理

- **触摸开始**: 初始化拖动时间
- **触摸移动**: 只更新临时时间，不影响实际播放时间
- **触摸结束**: 使用临时时间进行跳转

### 5. 修改时间显示

让时间显示在拖动时显示拖动时间：

```vue
<text class="current-time">{{ formatTime(isDragging ? dragCurrentTime : currentTime) }}</text>
```

## 修复效果

✅ **拖动流畅**: 进度条拖动时不再跳动
✅ **实时预览**: 拖动时可以实时看到目标时间
✅ **精确跳转**: 松手时准确跳转到目标位置
✅ **状态隔离**: 拖动状态和播放状态完全分离

## 技术要点

1. **状态管理**: 使用 `isDragging` 标志区分不同状态
2. **时间隔离**: 拖动时间和播放时间分离管理
3. **事件优化**: 避免多个事件同时修改同一个响应式变量
4. **用户体验**: 提供实时的视觉反馈

## 测试建议

1. **基础拖动**: 测试进度条拖动是否流畅
2. **边界情况**: 测试拖动到开头和结尾
3. **播放状态**: 测试播放中和暂停时的拖动
4. **快速拖动**: 测试快速连续拖动
5. **长时间音频**: 测试长音频的精确跳转

这个修复确保了进度条拖动的流畅性和准确性，提升了用户的播放体验。
