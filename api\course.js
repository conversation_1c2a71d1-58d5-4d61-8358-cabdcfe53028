import request from '../utils/request'

// 获取课程列表
export function getCourseList(params) {
  return request({
    url: '/miniapp/course/list',
    method: 'get',
    params
  })
}

// 获取课程详情
export function getCourseDetail(id) {
  return request({
    url: `/miniapp/course/${id}`,
    method: 'get'
  })
}

// 获取课程章节列表
export function getCourseChapters(courseId) {
  return request({
    url: `/miniapp/course/chapters/${courseId}`,
    method: 'get'
  })
}

// 获取章节详情
export function getChapterDetail(id) {
  return request({
    url: `/miniapp/course/chapter/${id}`,
    method: 'get'
  })
}

// 更新学习进度
export function updateProgress(data) {
  return request({
    url: '/miniapp/course/progress',
    method: 'post',
    data
  })
}

// 创建课程订单
export function createCourseOrder(courseId) {
  return request({
    url: `/miniapp/course/order/${courseId}`,
    method: 'post'
  })
}

// 提交课程评价
export function submitCourseReview(data) {
  return request({
    url: '/miniapp/course/review',
    method: 'post',
    data
  })
}

// 获取课程评价列表
export function getCourseReviews(courseId) {
  return request({
    url: `/miniapp/course/reviews/${courseId}`,
    method: 'get'
  })
}

// 获取课程评价统计
export function getCourseReviewStats(courseId) {
  return request({
    url: `/miniapp/course/reviews/${courseId}`,
    method: 'get'
  })
}

// 获取用户已购课程列表
export function getPurchasedCourses() {
  return request({
    url: '/miniapp/course/purchased',
    method: 'get'
  })
}

// 获取用户学习进度列表
export function getUserProgress(courseId) {
  return request({
    url: `/miniapp/course/progress/${courseId}`,
    method: 'get'
  })
}

// 获取相关课程推荐
export function getRecommendedCourses(courseId, limit = 5) {
  return request({
    url: `/miniapp/course/recommended/${courseId}`,
    method: 'get',
    params: { limit }
  })
}
