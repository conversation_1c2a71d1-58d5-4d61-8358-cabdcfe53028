<template>
	<view class="review-item" :class="{ 'card-style': showCard }">
		<view class="review-header">
			<view class="logo-container">
				<image class="logo-img" :src="logoSrc" mode="aspectFill" />
			</view>
			<view class="user-info">
				<text class="username">{{ maskUsername(review.username) }}</text>
				<view class="rating-container">
					<text class="rating-label">评分</text>
					<uni-rate color="#E6E6E6" active-color='#A04571' :value="review.rating" size="12" readonly />
				</view>
			</view>
		</view>
		<text class="review-content">{{ truncateText(review.content, contentMaxLength) }}</text>
		<view class="review-footer">
			<text class="consult-type">{{ consultTypeLabel }}: {{ review.consultType || defaultConsultType }}</text>
			<text class="review-date">{{ formatReviewDate(review.reviewTime) }}</text>
		</view>

		<!-- 咨询师回复 -->
		<view class="consultant-reply" v-if="showReply && review.consultantReply">
			<view class="reply-header">
				<text class="reply-label">咨询师回复：</text>
			</view>
			<text class="reply-content">{{ review.consultantReply }}</text>
		</view>
	</view>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
	review: {
		type: Object,
		required: true,
		default: () => ({})
	},
	contentMaxLength: {
		type: Number,
		default: 50
	},
	showLogo: {
		type: Boolean,
		default: true
	},
	logoSrc: {
		type: String,
		default: '../../static/icon/店铺logo.png'
	},
	showReply: {
		type: Boolean,
		default: false
	},
	// 咨询类别标签文本
	consultTypeLabel: {
		type: String,
		default: '咨询类别'
	},
	// 默认咨询类别
	defaultConsultType: {
		type: String,
		default: '情感焦虑'
	},
	showCard: {
		type: Boolean,
		default: true
	}
})

// 用户名脱敏
const maskUsername = (username) => {
	if (!username) return '匿名用户'
	if (username.length <= 2) return username
	return username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1)
}

// 截断文本
const truncateText = (text, maxLength) => {
	if (!text) return ''
	if (text.length <= maxLength) return text
	return text.substring(0, maxLength) + '...'
}

// 评价日期格式化 - iOS 兼容版本
const formatReviewDate = (dateString) => {
	if (!dateString) return ''
	try {
		// 将 "2025-07-27 12:16:10" 格式转换为 iOS 兼容的格式
		let formattedDateStr = dateString

		// 如果是 "yyyy-MM-dd HH:mm:ss" 格式，转换为 "yyyy/MM/dd HH:mm:ss"
		if (dateString.includes(' ') && dateString.includes('-')) {
			formattedDateStr = dateString.replace(/-/g, '/')
		}
		// 如果只是 "yyyy-MM-dd" 格式，转换为 "yyyy/MM/dd"
		else if (dateString.includes('-') && !dateString.includes(' ')) {
			formattedDateStr = dateString.replace(/-/g, '/')
		}

		const date = new Date(formattedDateStr)
		if (isNaN(date.getTime())) {
			console.warn('Invalid review date format:', dateString)
			return dateString // 返回原始字符串作为备用
		}
		return `${date.getFullYear()}/${String(date.getMonth() + 1).padStart(2, '0')}/${String(date.getDate()).padStart(2, '0')}`
	} catch (error) {
		console.error('评价日期格式化错误:', error)
		return dateString // 返回原始字符串作为备用
	}
}
</script>

<style lang="scss" scoped>
.review-item {
	background: #fff;
	border-radius: 16rpx;
	margin-bottom: 16rpx;

	&.card-style {
		padding: 24rpx;
	}

	.review-header {
		display: flex;
		align-items: center;
		margin-bottom: 16rpx;

		.logo-container {
			margin-right: 16rpx;

			.logo-img {
				width: 50rpx;
				height: 50rpx;
				border-radius: 25rpx;
			}
		}

		.user-info {
			flex: 1;

			.username {
				font-size: 28rpx;
				font-weight: 600;
				color: #333;
				display: block;
				margin-bottom: 8rpx;
			}

			.rating-container {
				display: flex;
				align-items: center;

				.rating-label {
					font-size: 24rpx;
					color: #666;
					margin-right: 8rpx;
				}
			}
		}
	}

	.review-content {
		font-size: 26rpx;
		color: #666;
		line-height: 1.5;
		margin-bottom: 16rpx;
		display: block;
	}

	.review-footer {
		display: flex;
		justify-content: space-between;
		align-items: center;

		.consult-type {
			font-size: 22rpx;
			color: #999;
		}

		.review-date {
			font-size: 22rpx;
			color: #999;
		}
	}

	.consultant-reply {
		margin-top: 16rpx;
		padding: 16rpx;
		background: #f8f9fa;
		border-radius: 8rpx;
		border-left: 4rpx solid #b85a9b;

		.reply-header {
			margin-bottom: 8rpx;

			.reply-label {
				font-size: 24rpx;
				font-weight: 600;
				color: #b85a9b;
			}
		}

		.reply-content {
			font-size: 24rpx;
			color: #666;
			line-height: 1.5;
		}
	}
}
</style>
