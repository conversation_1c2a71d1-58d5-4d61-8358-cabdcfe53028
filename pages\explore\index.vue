<template>
  <view class="explore-container">
    <!-- 搜索栏 -->
    <view class="search-wrapper" :style="{
      paddingTop: `${statusBarHeight}px`,
      height: `${navBarHeight}px`
    }">
      <view class="search-box" :style="{
        width: `${windowWidth - menuRight - menuWidth - 30}px`,
        height: `${menuHeight}px`,
        marginTop: `${menuTop - statusBarHeight}px`
      }" @click="toSearch">
        <uni-icons type="search" size="16" color="#999"></uni-icons>
        <view class="search-placeholder">搜索</view>
      </view>
    </view>

    <!-- 标签切换 -->
    <view class="tab-wrapper">
      <view v-for="(tab, index) in tabs" :key="index" class="tab-item" :class="{ active: currentTab === index }"
        @click="switchTab(index)">
        {{ tab.name }}
      </view>
    </view>

    <!-- 分类列表 -->
    <scroll-view class="category-scroll" scroll-x show-scrollbar="false" :scroll-left="scrollLeft">
      <view class="category-item" :class="{ active: currentCategory === -1 }" @click="switchCategory(-1)">
        全部
      </view>
      <view v-for="(category, index) in categories" :key="category.categoryId" class="category-item"
        :class="{ active: currentCategory === index }" @click="switchCategory(index)">
        {{ category.categoryName }}
        <text class="count">({{ getCategoryItemCount(category) }})</text>
      </view>
    </scroll-view>

    <!-- 轮播图 -->
    <swiper class="banner" circular :indicator-dots="true" indicator-color="rgba(255, 255, 255, 0.6)"
      indicator-active-color="#ffffff" :autoplay="true" :interval="3000" :duration="500">
      <swiper-item v-for="(item, index) in bannerList" :key="index">
        <image :src="item.imageUrl || defaultImage" mode="aspectFill" @click="viewDetail(item)"></image>
      </swiper-item>
    </swiper>

    <!-- 内容列表 -->
    <scroll-view class="content-list" scroll-y @scrolltolower="loadMore">
      <view v-for="item in currentList" :key="item.id" class="list-item" @click="viewDetail(item)">
        <image :src="item.imageUrl || defaultImage" mode="aspectFill" class="item-image"></image>
        <view class="item-info">
          <text class="item-title">{{ getItemTitle(item) }}</text>
          <text class="item-desc">{{ getItemDescription(item) }}</text>
          <view class="item-meta">
            <text class="question-count" v-if="tabs[currentTab]?.type === 'assessment'">{{ item.questionCount || 0
            }}道题</text>
            <text class="duration" v-else-if="tabs[currentTab]?.type === 'meditation'">{{ item.duration || 0 }}分钟</text>
            <text class="price" v-if="getItemPrice(item) > 0">¥{{ getItemPrice(item) }}</text>
            <text class="free" v-else>免费</text>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <uni-load-more :status="loadingStatus"></uni-load-more>
    </scroll-view>

    <cc-myTabbar :tabBarShow="3"></cc-myTabbar>
  </view>
</template>

<script setup>
// 导入所需的组件和API
import { ref, onMounted, computed } from 'vue'
import { onLoad, onReady } from "@dcloudio/uni-app"
import { getCategoryTree } from '@/api/category'
import { listAssessment } from '@/api/evaluation'
import { getMeditationList } from '@/api/meditation'

// 导航栏相关数据
const statusBarHeight = ref(0)
const menuButtonInfo = ref({})
const navBarHeight = ref(0)
const menuRight = ref(0)
const menuTop = ref(0)
const menuHeight = ref(32)
const menuWidth = ref(87) // 默认胶囊按钮宽度
const windowWidth = ref(375)

// 获取系统信息并计算导航栏高度
const initNavigationBar = () => {
  // #ifdef MP-WEIXIN
  try {
    // 获取窗口信息
    const windowInfo = uni.getWindowInfo()
    windowWidth.value = windowInfo.windowWidth

    // 获取系统信息
    const systemInfo = uni.getAppBaseInfo()
    statusBarHeight.value = windowInfo.statusBarHeight

    // 获取胶囊按钮位置信息
    const menuInfo = uni.getMenuButtonBoundingClientRect()
    menuButtonInfo.value = menuInfo
    menuRight.value = windowWidth.value - menuInfo.right
    menuHeight.value = menuInfo.height
    menuTop.value = menuInfo.top
    menuWidth.value = menuInfo.width

    // 计算导航栏高度
    navBarHeight.value = menuInfo.height + (menuInfo.top - statusBarHeight.value) * 2
  } catch (error) {
    console.error('获取系统信息失败:', error)
    // 使用默认值
    statusBarHeight.value = 20
    navBarHeight.value = 44
    menuRight.value = 7
    menuHeight.value = 32
    menuTop.value = statusBarHeight.value + 6
    menuWidth.value = 87 // 默认胶囊按钮宽度
  }
  // #endif

  // #ifdef H5 || APP-PLUS
  try {
    const windowInfo = uni.getWindowInfo()
    windowWidth.value = windowInfo.windowWidth
    statusBarHeight.value = windowInfo.statusBarHeight

    const systemInfo = uni.getAppBaseInfo()
    const isIOS = systemInfo.platform.toLowerCase() === 'ios'

    navBarHeight.value = isIOS ? 44 : 48
    menuRight.value = 10
    menuHeight.value = 32
    menuTop.value = statusBarHeight.value + (isIOS ? 6 : 8)
    menuWidth.value = 87 // 默认胶囊按钮宽度
  } catch (error) {
    console.error('获取系统信息失败:', error)
    // 使用默认值
    statusBarHeight.value = 20
    navBarHeight.value = 44
    menuRight.value = 10
    menuHeight.value = 32
    menuTop.value = 26
    menuWidth.value = 87
  }
  // #endif
}

// 基础数据
const searchKeyword = ref('')
const tabs = ref([])  // 从API动态加载
const currentTab = ref(0)
const currentCategory = ref(-1) // -1 表示全部
const scrollLeft = ref(0)
const loadingStatus = ref('more')

// 默认图片
const defaultImage = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-image.png'

// 分类数据
const fullCategoryData = ref([])  // 完整的分类树数据
const categoryDataMap = ref({})   // 分类数据映射，按分类ID存储数据

// 计算属性
const currentTabData = computed(() => {
  if (tabs.value.length === 0) {
    return { categories: [], allItems: [] }
  }

  const currentTabInfo = tabs.value[currentTab.value]
  if (!currentTabInfo) {
    return { categories: [], allItems: [] }
  }

  // 获取所有分类的数据
  let allItems = []
  const categories = currentTabInfo.children || []

  // 如果有分类，从分类数据映射中获取数据
  if (categories.length > 0) {
    categories.forEach(category => {
      const categoryData = categoryDataMap.value[category.categoryId] || []
      allItems = allItems.concat(categoryData)
    })
  } else {
    // 如果没有分类，获取全部数据
    allItems = categoryDataMap.value['all'] || []
  }

  const result = {
    categories: categories,
    allItems: allItems
  }

  console.log('当前Tab数据:', {
    tabName: currentTabInfo.name,
    categoriesCount: result.categories.length,
    itemsCount: result.allItems.length,
    categoryDataKeys: Object.keys(categoryDataMap.value)
  })

  return result
})

const categories = computed(() => {
  return currentTabData.value.categories
})

const bannerList = computed(() => {
  const allItems = currentTabData.value.allItems
  return allItems.slice(0, 3)
})

const currentList = computed(() => {
  if (currentCategory.value === -1) {
    // 全部
    return currentTabData.value.allItems
  } else {
    // 特定分类
    const category = categories.value[currentCategory.value]
    if (category) {
      return categoryDataMap.value[category.categoryId] || []
    }
    return []
  }
})



// 计算分类项目数量
const getCategoryItemCount = (category) => {
  const categoryData = categoryDataMap.value[category.categoryId] || []
  return categoryData.length
}

// 获取项目标题
const getItemTitle = (item) => {
  return item.scaleName || item.name || item.title || item.courseName || '未知标题'
}

// 获取项目描述
const getItemDescription = (item) => {
  return item.description || item.introduction || item.summary || '暂无描述'
}

// 获取项目价格
const getItemPrice = (item) => {
  if (item.payMode === 1 || item.isFree === 0) {
    return parseFloat(item.price || 0)
  }
  return 0
}

// 页面方法
const toSearch = () => {
  const searchType = tabs.value[currentTab.value]?.type || 'assessment'
  uni.navigateTo({
    url: `/pages/search/search?type=${searchType}`
  })
}

const switchTab = async (index) => {
  currentTab.value = index
  currentCategory.value = -1  // 重置为全部
  console.log('切换Tab:', tabs.value[index]?.name)

  // 清空当前数据
  categoryDataMap.value = {}

  // 重新加载数据
  await loadTabData()
}

const switchCategory = (index) => {
  currentCategory.value = index
  const categoryName = index === -1 ? '全部' : categories.value[index]?.categoryName
  console.log('切换分类:', categoryName)
}

const loadMore = () => {
  // 由于使用分类树接口，数据一次性加载，不需要分页
  console.log('已加载全部数据')
}

const viewDetail = (item) => {
  const currentTabType = tabs.value[currentTab.value]?.type

  if (currentTabType === 'assessment') {
    // 测评详情
    uni.navigateTo({
      url: `/pages/evaluation/detail/index?id=${item.id}`
    })
  } else if (currentTabType === 'meditation') {
    // 冥想详情
    uni.navigateTo({
      url: `/pages/meditation/index?id=${item.id}`
    })
  } else {
    console.log('未知的内容类型:', item)
  }
}

// API调用相关方法
const loadData = async () => {
  try {
    loadingStatus.value = 'loading'
    await loadMainCategories()
    await loadTabData()
    loadingStatus.value = 'more'
  } catch (error) {
    console.error('加载数据失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
    loadingStatus.value = 'more'
  }
}

// 加载主分类数据（使用与分类页面相同的接口）
const loadMainCategories = async () => {
  try {
    console.log('开始加载分类数据...')
    const res = await getCategoryTree()
    console.log('API响应:', res)

    if (res.code === 200 && res.data && res.data.categories) {
      // 存储完整的分类数据
      fullCategoryData.value = res.data.categories
      console.log('完整分类数据:', res.data.categories)

      // 筛选出测评和冥想的分类，创建Tab
      const filteredTabs = []
      res.data.categories.forEach(category => {
        console.log('检查分类:', category.categoryName, '完整数据:', JSON.stringify(category, null, 2))
        if (category.categoryName === '测评' || category.categoryName === '冥想') {
          console.log('匹配到分类:', category.categoryName)
          console.log('分类的children:', category.children)

          // 检查children中的数据结构
          if (category.children && category.children.length > 0) {
            category.children.forEach((child, index) => {
              console.log(`子分类${index}:`, child.categoryName, '数据字段:', Object.keys(child))
            })
          }

          filteredTabs.push({
            name: category.categoryName,
            type: category.categoryName === '测评' ? 'assessment' : 'meditation',
            originalName: category.categoryName,
            categoryId: category.categoryId,
            children: category.children || []
          })
        }
      })

      tabs.value = filteredTabs

      console.log('分类数据加载成功:', {
        totalCategories: res.data.categories.length,
        filteredTabs: filteredTabs.length,
        tabs: tabs.value,
        fullData: res.data.categories.map(cat => ({
          name: cat.categoryName,
          childrenCount: cat.children?.length || 0
        }))
      })
    } else {
      console.error('API响应格式错误:', res)
    }
  } catch (error) {
    console.error('获取分类数据失败:', error)
  }
}

// 加载Tab对应的数据
const loadTabData = async () => {
  if (tabs.value.length === 0) {
    console.log('没有Tab数据，跳过数据加载')
    return
  }

  const currentTabInfo = tabs.value[currentTab.value]
  if (!currentTabInfo) {
    console.log('当前Tab信息不存在')
    return
  }

  console.log('开始加载Tab数据:', currentTabInfo.name, currentTabInfo.type)

  try {
    if (currentTabInfo.type === 'assessment') {
      // 加载测评数据
      const res = await listAssessment()
      if (res.code === 200) {
        categoryDataMap.value['all'] = res.data || []
        console.log('测评数据加载成功:', res.data?.length || 0)
      }
    } else if (currentTabInfo.type === 'meditation') {
      // 加载冥想数据
      const res = await getMeditationList()
      if (res.code === 200) {
        categoryDataMap.value['all'] = res.data || []
        console.log('冥想数据加载成功:', res.data?.length || 0)
      }
    }
  } catch (error) {
    console.error('加载Tab数据失败:', error)
  }
}

// 生命周期钩子
onLoad(() => {
  initNavigationBar() // 初始化导航栏
  loadData() // 加载分类和列表数据
})

onReady(() => {
  uni.hideTabBar()
})
</script>

<style lang="scss" scoped>
.explore-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;

  .search-wrapper {
    background: #fff;
    position: relative;

    .search-box {
      position: absolute;
      left: 30rpx;
      display: flex;
      align-items: center;
      background: #f5f5f5;
      border-radius: 36rpx;
      padding: 0 24rpx;
      box-sizing: border-box;

      .uni-icons {
        margin-right: 12rpx;
        flex-shrink: 0;
      }

      .search-placeholder {
        flex: 1;
        font-size: 28rpx;
        color: #999;
      }
    }
  }

  .tab-wrapper {
    display: flex;
    padding: 0 30rpx;
    background: #fff;
    border-bottom: 1rpx solid #eee;

    .tab-item {
      position: relative;
      padding: 24rpx 40rpx;
      font-size: 32rpx;
      color: #666;

      &.active {
        color: #333;
        font-weight: 500;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 48rpx;
          height: 4rpx;
          background: #409eff;
          border-radius: 2rpx;
        }
      }
    }
  }

  .category-scroll {
    white-space: nowrap;
    background: #fff;
    padding: 20rpx 0;

    .category-item {
      display: inline-block;
      padding: 12rpx 32rpx;
      margin: 0 16rpx;
      font-size: 28rpx;
      color: #666;
      background: #f5f5f5;
      border-radius: 28rpx;

      &:first-child {
        margin-left: 30rpx;
      }

      &:last-child {
        margin-right: 30rpx;
      }

      &.active {
        color: #fff;
        background: #409eff;
      }

      .count {
        font-size: 24rpx;
        margin-left: 4rpx;
      }
    }
  }

  .banner {
    height: 300rpx;
    padding: 30rpx;
    box-sizing: border-box;

    image {
      width: 100%;
      height: 100%;
      border-radius: 16rpx;
    }
  }

  .content-list {
    padding: 0 30rpx;
    width: calc(100% - 60rpx);

    .list-item {
      display: flex;
      background: #fff;
      border-radius: 16rpx;
      padding: 24rpx;
      margin-bottom: 20rpx;

      .item-image {
        width: 200rpx;
        height: 200rpx;
        border-radius: 12rpx;
        margin-right: 24rpx;
      }

      .item-info {
        flex: 1;
        display: flex;
        flex-direction: column;

        .item-title {
          font-size: 32rpx;
          font-weight: 500;
          color: #333;
          margin-bottom: 12rpx;
        }

        .item-desc {
          font-size: 26rpx;
          color: #666;
          margin-bottom: 24rpx;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }

        .item-meta {
          margin-top: auto;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .question-count,
          .duration {
            font-size: 24rpx;
            color: #409eff;
          }

          .price {
            font-size: 28rpx;
            color: #ff6b6b;
            font-weight: 500;
          }

          .free {
            font-size: 24rpx;
            color: #52c41a;
          }
        }
      }
    }
  }
}
</style>
