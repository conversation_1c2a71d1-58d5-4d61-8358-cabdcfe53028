<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类树数据测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
            line-height: 1.4;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>分类树数据结构测试</h1>
        
        <div class="section">
            <h3>API测试</h3>
            <button class="btn" onclick="testCategoryTree()">测试 getCategoryTree API</button>
            <div id="status" class="status" style="display: none;"></div>
        </div>

        <div class="section">
            <h3>完整API响应</h3>
            <pre id="fullResponse">点击上方按钮获取数据...</pre>
        </div>

        <div class="section">
            <h3>测评分类数据</h3>
            <pre id="assessmentData">等待数据加载...</pre>
        </div>

        <div class="section">
            <h3>冥想分类数据</h3>
            <pre id="meditationData">等待数据加载...</pre>
        </div>

        <div class="section">
            <h3>数据结构分析</h3>
            <pre id="dataAnalysis">等待数据加载...</pre>
        </div>
    </div>

    <script>
        // 模拟request函数
        function request(config) {
            const baseURL = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com'; // 替换为实际的API地址
            const url = baseURL + config.url;
            
            return fetch(url, {
                method: config.method || 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    // 添加其他必要的headers
                }
            }).then(response => response.json());
        }

        // 获取分类树数据
        function getCategoryTree() {
            return request({
                url: '/psy/category/treeWithProducts',
                method: 'get'
            });
        }

        function showStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.style.display = 'block';
            statusEl.className = `status ${type}`;
            statusEl.textContent = message;
        }

        function updateElement(id, content) {
            document.getElementById(id).textContent = content;
        }

        async function testCategoryTree() {
            try {
                showStatus('正在加载分类数据...', 'loading');
                
                const response = await getCategoryTree();
                
                showStatus('数据加载成功！', 'success');
                
                // 显示完整响应
                updateElement('fullResponse', JSON.stringify(response, null, 2));
                
                if (response.code === 200 && response.data && response.data.categories) {
                    const categories = response.data.categories;
                    
                    // 查找测评和冥想数据
                    const assessmentCategory = categories.find(cat => cat.categoryName === '测评');
                    const meditationCategory = categories.find(cat => cat.categoryName === '冥想');
                    
                    // 显示测评数据
                    if (assessmentCategory) {
                        updateElement('assessmentData', JSON.stringify(assessmentCategory, null, 2));
                    } else {
                        updateElement('assessmentData', '未找到测评分类');
                    }
                    
                    // 显示冥想数据
                    if (meditationCategory) {
                        updateElement('meditationData', JSON.stringify(meditationCategory, null, 2));
                    } else {
                        updateElement('meditationData', '未找到冥想分类');
                    }
                    
                    // 数据结构分析
                    let analysis = '分类数据分析:\n\n';
                    analysis += `总分类数: ${categories.length}\n`;
                    analysis += '所有分类名称: ' + categories.map(cat => cat.categoryName).join(', ') + '\n\n';
                    
                    categories.forEach(category => {
                        analysis += `分类: ${category.categoryName}\n`;
                        analysis += `  - categoryId: ${category.categoryId}\n`;
                        analysis += `  - children数量: ${category.children ? category.children.length : 0}\n`;
                        
                        if (category.children && category.children.length > 0) {
                            analysis += '  - 子分类:\n';
                            category.children.forEach(child => {
                                analysis += `    * ${child.categoryName} (ID: ${child.categoryId})\n`;
                                
                                // 检查子分类中的数据字段
                                const dataFields = Object.keys(child).filter(key => 
                                    Array.isArray(child[key]) && child[key].length > 0
                                );
                                if (dataFields.length > 0) {
                                    analysis += `      数据字段: ${dataFields.join(', ')}\n`;
                                    dataFields.forEach(field => {
                                        analysis += `      ${field}: ${child[field].length}项\n`;
                                    });
                                }
                            });
                        }
                        analysis += '\n';
                    });
                    
                    updateElement('dataAnalysis', analysis);
                    
                } else {
                    showStatus('API响应格式错误', 'error');
                    updateElement('assessmentData', '数据格式错误');
                    updateElement('meditationData', '数据格式错误');
                    updateElement('dataAnalysis', '无法分析数据结构');
                }
                
            } catch (error) {
                showStatus(`请求失败: ${error.message}`, 'error');
                console.error('API请求失败:', error);
                updateElement('fullResponse', `错误: ${error.message}`);
                updateElement('assessmentData', '请求失败');
                updateElement('meditationData', '请求失败');
                updateElement('dataAnalysis', '请求失败');
            }
        }

        // 页面加载时自动测试
        window.onload = function() {
            console.log('页面加载完成，准备测试API...');
        };
    </script>
</body>
</html>
