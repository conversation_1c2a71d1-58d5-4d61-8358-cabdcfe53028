# 阿里云RTC音视频通话集成文档

## 概述

本文档描述了如何在心理咨询小程序中集成阿里云RTC音视频通话功能，包括服务端API设计和前端集成方案。

## 服务端API接口

### 1. 生成RTC Token

**接口地址**: `POST /api/rtc/token`

**请求参数**:
```json
{
  "channelId": "consultation_123_1640995200000",
  "userId": "user123",
  "role": "consultant" // consultant | client
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "token": "rtc_token_string",
    "channelId": "consultation_123_1640995200000",
    "expireTime": 1640998800
  }
}
```

### 2. 创建通话房间

**接口地址**: `POST /api/rtc/create-room`

**请求参数**:
```json
{
  "appointmentId": 123,
  "consultantId": "consultant123",
  "clientId": "client456",
  "callType": "video" // video | voice
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "channelId": "consultation_123_1640995200000",
    "roomId": "room789"
  }
}
```

### 3. 开始通话

**接口地址**: `POST /api/rtc/start-call`

**请求参数**:
```json
{
  "appointmentId": 123,
  "channelId": "consultation_123_1640995200000",
  "callType": "video"
}
```

### 4. 接听通话

**接口地址**: `POST /api/rtc/accept-call`

**请求参数**:
```json
{
  "channelId": "consultation_123_1640995200000",
  "userId": "user123"
}
```

### 5. 拒绝通话

**接口地址**: `POST /api/rtc/reject-call`

**请求参数**:
```json
{
  "channelId": "consultation_123_1640995200000",
  "userId": "user123"
}
```

### 6. 取消通话

**接口地址**: `POST /api/rtc/cancel-call`

**请求参数**:
```json
{
  "channelId": "consultation_123_1640995200000",
  "userId": "user123"
}
```

### 7. 结束通话

**接口地址**: `POST /api/rtc/end-call`

**请求参数**:
```json
{
  "channelId": "consultation_123_1640995200000",
  "duration": 1800 // 通话时长（秒）
}
```

## 数据库表设计

### 通话房间表 (call_rooms)

```sql
CREATE TABLE call_rooms (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  channel_id VARCHAR(100) NOT NULL UNIQUE,
  consultation_id BIGINT,
  consultant_id BIGINT,
  client_id BIGINT,
  status VARCHAR(20) DEFAULT 'waiting', -- waiting, in_progress, completed, cancelled
  call_type VARCHAR(10) DEFAULT 'video', -- video, voice
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  start_time TIMESTAMP NULL,
  end_time TIMESTAMP NULL,
  duration INT DEFAULT 0 -- 通话时长（秒）
);
```

### 通话参与者表 (call_participants)

```sql
CREATE TABLE call_participants (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  channel_id VARCHAR(100) NOT NULL,
  user_id BIGINT NOT NULL,
  role VARCHAR(20) NOT NULL, -- consultant, client
  join_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  leave_time TIMESTAMP NULL,
  status VARCHAR(20) DEFAULT 'joined' -- joined, left
);
```

### 通话记录表 (call_records)

```sql
CREATE TABLE call_records (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  channel_id VARCHAR(100) NOT NULL,
  consultation_id BIGINT,
  consultant_id BIGINT,
  client_id BIGINT,
  call_type VARCHAR(10),
  start_time TIMESTAMP,
  end_time TIMESTAMP,
  duration INT DEFAULT 0,
  status VARCHAR(20), -- completed, interrupted, failed
  quality_score INT, -- 通话质量评分 1-5
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 前端集成要点

### 1. 小程序权限配置

在 `manifest.json` 中添加权限：

```json
{
  "mp-weixin": {
    "permission": {
      "scope.camera": {
        "desc": "用于视频通话功能"
      },
      "scope.record": {
        "desc": "用于语音通话功能"
      }
    }
  }
}
```

### 2. 组件使用

- **live-pusher**: 用于推送本地音视频流
- **live-player**: 用于播放远端音视频流

### 3. 关键功能

1. **Token管理**: 自动刷新Token，处理过期
2. **网络监控**: 监控网络质量，提示用户
3. **状态管理**: 管理通话状态（连接中、通话中、已断开）
4. **错误处理**: 处理网络异常、设备权限等问题
5. **通话记录**: 记录通话时长和质量

### 4. 页面结构

- `pages/video-call/index.vue` - 视频通话页面
- `pages/call-invitation/index.vue` - 通话邀请页面
- `pages/voice-call/index.vue` - 语音通话页面（可选）

## 部署配置

### 1. 阿里云RTC配置

1. 开通阿里云RTC服务
2. 获取AppID和AppKey
3. 配置回调地址
4. 设置安全域名

### 2. 服务端配置

```yaml
# application.yml
aliyun:
  rtc:
    app-id: your_app_id
    app-key: your_app_key
    app-secret: your_app_secret
    region: cn-hangzhou
```

### 3. 小程序配置

在小程序管理后台配置：
- 服务器域名
- 业务域名
- UDP域名（RTC专用）

## 测试方案

### 1. 功能测试

- [ ] Token生成和验证
- [ ] 房间创建和加入
- [ ] 音视频推拉流
- [ ] 通话状态管理
- [ ] 网络异常处理

### 2. 性能测试

- [ ] 并发通话数量
- [ ] 音视频质量
- [ ] 延迟测试
- [ ] 资源占用

### 3. 兼容性测试

- [ ] 不同手机型号
- [ ] 不同网络环境
- [ ] 不同小程序版本

## 注意事项

1. **权限申请**: 首次使用需要申请摄像头和麦克风权限
2. **网络要求**: 建议在WiFi或4G网络下使用
3. **设备兼容**: 部分低端设备可能不支持高清视频
4. **费用控制**: 按通话时长计费，需要做好费用监控
5. **隐私保护**: 通话内容不会被录制或存储

## 后续优化

1. **AI降噪**: 集成语音降噪功能
2. **美颜滤镜**: 添加视频美颜效果
3. **屏幕共享**: 支持共享心理测评结果
4. **多人通话**: 支持家庭咨询场景
5. **通话录制**: 征得同意后录制咨询过程
