# ReviewItem组件动态标签优化

## 问题描述

用户反馈：
> "这里应该是动态的，因为别的页面也要用组件"

**原始问题**：
```vue
<text class="consult-type">咨询类别: {{ review.consultType || '情感焦虑' }}</text>
```

**问题分析**：
- ReviewItem组件被多个页面使用（课程详情、咨询师详情、测评详情等）
- 硬编码的"咨询类别"标签不适合所有使用场景
- 默认值"情感焦虑"也不适合课程和测评场景
- 组件缺乏灵活性，不符合通用组件设计原则

## 修复方案

### 1. 组件Props扩展

**添加动态标签配置**：
```javascript
// 咨询类别标签文本
consultTypeLabel: {
  type: String,
  default: '咨询类别'
},
// 默认咨询类别
defaultConsultType: {
  type: String,
  default: '情感焦虑'
}
```

### 2. 模板动态化

**修改模板使用动态标签**：
```vue
<text class="consult-type">{{ consultTypeLabel }}: {{ review.consultType || defaultConsultType }}</text>
```

### 3. 各页面适配

#### 🎓 **课程详情页面**
```vue
<ReviewItem 
  v-for="review in recentReviews.slice(0, 2)" 
  :key="review.id" 
  :review="review"
  :content-max-length="50" 
  :showCard="false" 
  :consultTypeLabel="'课程类别'" 
  :defaultConsultType="'心理课程'" 
/>
```

#### 👨‍⚕️ **咨询师详情页面**
```vue
<ReviewItem 
  v-for="review in recentReviews.slice(0, 2)" 
  :key="review.id" 
  :review="review"
  :content-max-length="50" 
  :showCard="false" 
  :consultTypeLabel="'咨询类别'" 
  :defaultConsultType="'情感焦虑'" 
/>
```

#### 📊 **测评详情页面**
```vue
<ReviewItem 
  v-for="review in recentReviews.slice(0, 2)" 
  :key="review.id" 
  :review="review"
  :content-max-length="50" 
  :showCard="false" 
  :consultTypeLabel="'测评类别'" 
  :defaultConsultType="'心理测评'" 
/>
```

#### 📝 **咨询师评价页面**
```vue
<ReviewItem 
  v-for="review in filteredReviews" 
  :key="review.id" 
  :review="review"
  :content-max-length="200"
  :show-logo="false" 
  :show-reply="true" 
  :show-card="true" 
  :consultTypeLabel="'咨询类别'" 
  :defaultConsultType="'情感焦虑'" 
/>
```

## 使用场景对比

| 页面类型 | 标签文本 | 默认类别 | 使用场景 |
|---------|---------|---------|----------|
| **课程详情** | `课程类别` | `心理课程` | 显示课程相关评价 |
| **咨询师详情** | `咨询类别` | `情感焦虑` | 显示咨询服务评价 |
| **测评详情** | `测评类别` | `心理测评` | 显示测评相关评价 |
| **咨询师评价** | `咨询类别` | `情感焦虑` | 完整的咨询评价列表 |

## 技术要点

### 🔧 **组件设计原则**
- **通用性**：一个组件适配多种使用场景
- **可配置性**：通过props控制显示内容
- **向后兼容**：保持默认值确保现有代码正常工作
- **语义化**：标签文本符合具体使用场景

### 🎯 **Props设计**
- **consultTypeLabel**：控制标签文本（如"咨询类别"、"课程类别"）
- **defaultConsultType**：控制默认类别（如"情感焦虑"、"心理课程"）
- **灵活配置**：每个使用场景都可以自定义标签和默认值

### 🔄 **数据流**
1. **优先使用**：`review.consultType`（实际数据）
2. **降级使用**：`defaultConsultType`（页面指定的默认值）
3. **最终降级**：组件内置默认值

### 🎨 **显示效果**
- **课程页面**：`课程类别: 心理课程`
- **咨询师页面**：`咨询类别: 情感焦虑`
- **测评页面**：`测评类别: 心理测评`

## 实现优势

### ✅ **通用性提升**
- 一个组件适配所有评价显示场景
- 减少代码重复，提高维护性
- 统一的评价显示风格

### ✅ **灵活性增强**
- 每个页面可以自定义标签文本
- 支持不同的默认类别设置
- 易于扩展新的使用场景

### ✅ **用户体验优化**
- 标签文本更符合页面语境
- 默认值更贴合实际使用场景
- 信息显示更加准确和有意义

### ✅ **开发体验改善**
- 组件接口清晰明确
- 配置简单直观
- 向后兼容性好

## 测试验证

### 🔍 **功能测试**
1. **课程详情页**：验证显示"课程类别: 心理课程"
2. **咨询师详情页**：验证显示"咨询类别: 情感焦虑"
3. **测评详情页**：验证显示"测评类别: 心理测评"
4. **咨询师评价页**：验证显示"咨询类别: 情感焦虑"

### 🎨 **显示测试**
1. **有数据时**：显示实际的consultType值
2. **无数据时**：显示对应页面的默认值
3. **标签文本**：根据页面类型显示正确的标签

### 🔄 **兼容性测试**
1. **现有代码**：不传props时使用默认值
2. **新代码**：传入props时使用自定义值
3. **数据格式**：支持各种数据结构

现在ReviewItem组件已经完全动态化，可以根据不同的使用场景显示合适的标签和默认值！
