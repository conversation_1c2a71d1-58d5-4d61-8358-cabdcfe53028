<template>
  <view class="meditation-player">
    <!-- 内容区域 -->
    <view class="content-container">
      <!-- 冥想封面图片 -->
      <view class="cover-container">
        <view class="cover-wrapper">
          <!-- <image :src="meditationDetail.coverImage || defaultCover" mode="aspectFill" class="cover-image"></image> -->
          <view class="play-overlay" @click="togglePlay">
            <!-- <image :src="isPlaying ? '/static/icon/meditation/形状 6.png' : '/static/icon/meditation/形状 5.png'"
              class="play-icon"></image> -->
            <image class="play-overlay-bg" src="/static/icon/meditation/组 119.png" />
            <image class="play-overlay-icon" src="/static/icon/meditation/曲线 1.png" />
            <view class="play-overlay-circle">
              <view class="play-overlay-dot"></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 冥想信息 -->
      <view class="meditation-info">
        <view class="meditation-title">{{ meditationDetail.title || '高山流水' }}</view>
        <view class="meditation-subtitle">{{ meditationDetail.subtitle || '大自然冥想 深度催眠 放松解压' }}</view>
      </view>

      <!-- 进度条区域 -->
      <view class="progress-section">
        <view class="time-display">
          <text class="current-time">{{ formatTime(currentTime) }}</text>
          <text class="total-time">{{ formatTime(duration) }}</text>
        </view>
        <view class="progress-bar" @click="onProgressClick">
          <view class="progress-track" id="progress-track">
            <view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
            <view class="progress-thumb" :style="{ left: progressPercent + '%' }" @touchstart="onProgressTouchStart"
              @touchmove="onProgressTouchMove" @touchend="onProgressTouchEnd"></view>
          </view>
        </view>
      </view>

      <!-- 控制按钮区域 -->
      <view class="control-section">
        <!-- 左侧功能按钮 - 播放模式 -->
        <view class="side-controls left">
          <button class="side-btn" @click="togglePlayMode">
            <image :src="getPlayModeIcon()" class="side-icon"></image>
          </button>
        </view>

        <!-- 中间播放控制 -->
        <view class="main-controls">
          <button class="control-btn" @click="playPrevious">
            <image src="/static/icon/meditation/形状 11.png" class="control-icon"></image>
          </button>

          <button class="play-btn-main" @click="togglePlay">
            <image :src="isPlaying ? '/static/icon/meditation/形状 6.png' : '/static/icon/meditation/形状 5.png'"
              class="play-icon-main"></image>
          </button>

          <button class="control-btn" @click="playNext">
            <image src="/static/icon/meditation/组 119.png" class="control-icon"></image>
          </button>
        </view>

        <!-- 右侧功能按钮 - 播放列表 -->
        <view class="side-controls right">
          <button class="side-btn" @click="togglePlaylist">
            <image src="/static/icon/meditation/形状 9.png" class="side-icon"></image>
          </button>
        </view>
      </view>
    </view>

    <!-- 音频播放器已改为使用 wx.createInnerAudioContext -->

    <!-- 定时器弹窗 -->
    <uni-popup ref="timerPopup" type="bottom">
      <view class="timer-content">
        <view class="timer-title">设置定时关闭</view>
        <view class="timer-options">
          <view v-for="option in timerOptions" :key="option.value"
            :class="['timer-option', { active: selectedTimer === option.value }]" @click="selectTimer(option.value)">
            {{ option.label }}
          </view>
        </view>
        <view class="timer-actions">
          <button class="timer-btn cancel" @click="closeTimer">取消</button>
          <button class="timer-btn confirm" @click="confirmTimer">确定</button>
        </view>
      </view>
    </uni-popup>

    <!-- 播放列表弹窗 -->
    <uni-popup ref="playlistPopup" type="bottom">
      <view class="playlist-content">
        <view class="playlist-header">
          <text class="playlist-title">列表</text>
          <text class="playlist-order" @click="togglePlayMode">{{ getPlayModeText() }}</text>
          <uni-icons type="close" size="20" color="#666" @click="playlistPopup?.close()"></uni-icons>
        </view>
        <scroll-view class="playlist-scroll" scroll-y>
          <view class="playlist-item" v-for="(item, index) in playableAudios" :key="item.id"
            :class="{ active: currentTrackIndex === index }" @click="playTrack(index)">
            <view class="track-info">
              <text class="track-number">{{ String(index + 1).padStart(2, '0') }}</text>
              <view class="track-details">
                <text class="track-title">{{ item.audioName }}</text>
                <text class="track-duration">{{ formatTime(item.canPlayFull ? item.duration : item.playableDuration)
                  }}</text>
                <text v-if="!item.canPlayFull" class="trial-tag">试听</text>
              </view>
            </view>
            <uni-icons type="right" size="16" color="#ccc"></uni-icons>
          </view>
        </scroll-view>
      </view>
    </uni-popup>

    <!-- 倍速选择弹窗暂时隐藏 -->
    <!-- <uni-popup ref="speedPopup" type="bottom">
      <view class="speed-content">
        <view class="speed-title">选择播放速度</view>
        <view class="speed-options">
          <view
            v-for="speed in speedOptions"
            :key="speed.value"
            :class="['speed-option', { active: selectedSpeed === speed.value }]"
            @click="selectSpeed(speed.value)"
          >
            {{ speed.label }}
          </view>
        </view>
        <view class="speed-actions">
          <button class="speed-btn cancel" @click="closeSpeed">取消</button>
          <button class="speed-btn confirm" @click="confirmSpeed">确定</button>
        </view>
      </view>
    </uni-popup> -->
  </view>
</template>

<script setup>
import { ref, computed, onUnmounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import {
  getMeditationWithAudios,
  getPlayableAudios,
  playAudio,
  saveMeditationRecord,
  getAudioRecord
} from '@/api/meditation'
import { useUserStore } from '@/stores/user'
import { formatTime } from '@/utils/audioPlayer'

// 响应式数据
const meditationDetail = ref({})
const meditationId = ref(null)
const audioPlayer = ref(null)
const timerPopup = ref(null)
// const speedPopup = ref(null) // 倍速功能暂时隐藏

// 播放状态
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)
// const playbackRate = ref(1.0) // 倍速功能暂时隐藏
const isFavorited = ref(false)

// 定时器
const selectedTimer = ref(0)
const timerOptions = ref([
  { label: '不设置', value: 0 },
  { label: '15分钟', value: 15 },
  { label: '30分钟', value: 30 },
  { label: '45分钟', value: 45 },
  { label: '60分钟', value: 60 }
])
const timerInterval = ref(null)
const remainingTime = ref(0)

// 倍速设置（暂时隐藏）
// const selectedSpeed = ref(1.0)
// const speedOptions = ref([
//   { label: '0.5x', value: 0.5 },
//   { label: '0.75x', value: 0.75 },
//   { label: '1.0x (正常)', value: 1.0 },
//   { label: '1.25x', value: 1.25 },
//   { label: '1.5x', value: 1.5 },
//   { label: '2.0x', value: 2.0 }
// ])

// 同步selectedSpeed和playbackRate
// const syncSelectedSpeed = () => {
//   selectedSpeed.value = playbackRate.value
// }

// 默认封面图
const defaultCover = '/static/icon/meditation/形状 5 拷贝.png'

const userStore = useUserStore()

// 播放模式：0-顺序播放，1-单曲循环，2-随机播放
const playMode = ref(0)
const currentTrackIndex = ref(0)
const currentAudioId = ref(null)

// 播放列表弹窗引用
const playlistPopup = ref()

// 音频列表数据（从后端获取）
const audioList = ref([])
// 用户可播放的音频列表
const playableAudios = ref([])
// 当前播放的音频信息
const currentAudio = ref({})
// 用户是否已购买当前冥想
const isPurchased = ref(false)

// 计算属性
const progressPercent = computed(() => {
  if (duration.value === 0) return 0
  return (currentTime.value / duration.value) * 100
})

// 方法
const loadMeditationDetail = async () => {
  try {
    // 获取冥想详情（包含音频列表）
    const res = await getMeditationWithAudios(meditationId.value)
    if (res.code === 200) {
      const data = res.data
      meditationDetail.value = data
      audioList.value = data.audios || []
      isPurchased.value = data.purchased || false

      // 获取用户可播放的音频列表
      await loadPlayableAudios()

      // 设置第一个可播放的音频为当前播放
      if (playableAudios.value.length > 0) {
        const firstAudio = playableAudios.value[0]
        currentAudio.value = firstAudio
        currentAudioId.value = firstAudio.id
        currentTrackIndex.value = 0

        // 设置播放信息
        duration.value = firstAudio.canPlayFull ? firstAudio.duration : firstAudio.playableDuration

        // 初始化音频播放器
        if (firstAudio.audioUrl) {
          initAudioPlayer()
        }
      } else {
        uni.showToast({
          title: '暂无可播放的音频',
          icon: 'none'
        })
      }
    } else {
      uni.showToast({
        title: res.msg || '获取冥想详情失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取冥想详情失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

// 加载用户可播放的音频列表
const loadPlayableAudios = async () => {
  try {
    const res = await getPlayableAudios(meditationId.value)
    if (res.code === 200) {
      playableAudios.value = res.data || []
    }
  } catch (error) {
    console.error('获取可播放音频列表失败:', error)
  }
}

const togglePlay = () => {
  if (isPlaying.value) {
    pauseAudio()
  } else {
    playAudio()
  }
}

// 初始化音频播放器
const initAudioPlayer = () => {
  if (audioPlayer.value) {
    try {
      if (audioPlayer.value.destroy) {
        audioPlayer.value.destroy()
      } else if (audioPlayer.value.stop) {
        audioPlayer.value.stop()
      }
    } catch (e) {
      console.log('清理旧播放器失败:', e)
    }
  }

  const audioUrl = currentAudio.value.audioUrl || meditationDetail.value.audioUrl
  console.log('初始化音频播放器，URL:', audioUrl)

  // 直接使用背景音频管理器
  audioPlayer.value = uni.getBackgroundAudioManager()

  // 设置音频信息
  audioPlayer.value.title = currentAudio.value.audioName || meditationDetail.value.title || '冥想音频'
  audioPlayer.value.singer = '喜欢心理'
  audioPlayer.value.coverImgUrl = currentAudio.value.coverImage || meditationDetail.value.coverImage || ''
  audioPlayer.value.src = audioUrl

  // 绑定背景音频事件
  audioPlayer.value.onPlay(() => {
    console.log('音频开始播放')
    isPlaying.value = true
  })

  audioPlayer.value.onPause(() => {
    console.log('音频暂停')
    isPlaying.value = false
  })

  audioPlayer.value.onTimeUpdate(() => {
    currentTime.value = audioPlayer.value.currentTime || 0
    duration.value = audioPlayer.value.duration || 0
  })

  audioPlayer.value.onEnded(async () => {
    console.log('音频播放结束')
    isPlaying.value = false

    // 保存播放记录（标记为完成）
    if (userStore.isLoggedIn) {
      await savePlayRecord(true)
    }

    // 根据播放模式决定下一步操作
    if (playMode.value === 1) {
      // 单曲循环，重新播放当前音频
      setTimeout(() => {
        currentTime.value = 0
        audioPlayer.value?.play()
      }, 1000)
    } else if (playMode.value === 0 || playMode.value === 2) {
      // 顺序播放或随机播放，播放下一首
      const hasNext = currentTrackIndex.value < playableAudios.value.length - 1
      if (hasNext || playMode.value === 2) {
        setTimeout(() => {
          playNext()
        }, 1000)
      } else {
        uni.showToast({
          title: '播放完成',
          icon: 'success'
        })
      }
    }
  })

  audioPlayer.value.onError((error) => {
    console.error('音频播放错误:', error)
    uni.showToast({
      title: '音频播放失败',
      icon: 'none'
    })
  })

  // 检查倍速支持
  if ('playbackRate' in audioPlayer.value) {
    console.log('背景音频支持倍速功能')
  } else {
    console.log('背景音频不支持倍速功能')
  }
}

const playAudio = async () => {
  try {
    console.log('开始播放音频')

    // 记录播放开始
    if (userStore.isLoggedIn) {
      await playMeditation(meditationId.value, {
        startTime: currentTime.value,
        action: 'play'
      })
    }

    // 播放音频
    if (audioPlayer.value) {
      audioPlayer.value.play()
    }
  } catch (error) {
    console.error('播放失败:', error)
    uni.showToast({
      title: '播放失败',
      icon: 'none'
    })
  }
}

const pauseAudio = () => {
  console.log('暂停音频')
  if (audioPlayer.value) {
    audioPlayer.value.pause()
  }
}

const seekBackward = () => {
  const audioUrl = currentAudio.value.audioUrl || meditationDetail.value.audioUrl
  if (audioPlayer.value && audioUrl) {
    const newTime = Math.max(0, currentTime.value - 5)
    console.log('快退5秒:', currentTime.value, '->', newTime)

    try {
      // 停止当前播放，重新开始播放
      const wasPlaying = isPlaying.value
      const currentTitle = currentAudio.value.audioName || meditationDetail.value.title || '冥想音频'
      const currentSinger = '喜欢心理'
      const currentCover = currentAudio.value.coverImage || meditationDetail.value.coverImage || ''

      // 停止播放
      audioPlayer.value.stop()

      // 重新设置音频信息和开始时间
      setTimeout(() => {
        audioPlayer.value.title = currentTitle
        audioPlayer.value.singer = currentSinger
        audioPlayer.value.coverImgUrl = currentCover
        audioPlayer.value.startTime = newTime
        audioPlayer.value.src = audioUrl

        // 如果之前在播放，继续播放
        if (wasPlaying) {
          setTimeout(() => {
            audioPlayer.value.play()
          }, 100)
        }

        currentTime.value = newTime // 立即更新UI
      }, 100)

    } catch (error) {
      console.error('快退失败:', error)
      uni.showToast({
        title: '快退失败',
        icon: 'none'
      })
    }
  }
}

const seekForward = () => {
  const audioUrl = currentAudio.value.audioUrl || meditationDetail.value.audioUrl
  if (audioPlayer.value && audioUrl) {
    const newTime = Math.min(duration.value, currentTime.value + 5)
    console.log('快进5秒:', currentTime.value, '->', newTime)

    try {
      // 停止当前播放，重新开始播放
      const wasPlaying = isPlaying.value
      const currentTitle = currentAudio.value.audioName || meditationDetail.value.title || '冥想音频'
      const currentSinger = '喜欢心理'
      const currentCover = currentAudio.value.coverImage || meditationDetail.value.coverImage || ''

      // 停止播放
      audioPlayer.value.stop()

      // 重新设置音频信息和开始时间
      setTimeout(() => {
        audioPlayer.value.title = currentTitle
        audioPlayer.value.singer = currentSinger
        audioPlayer.value.coverImgUrl = currentCover
        audioPlayer.value.startTime = newTime
        audioPlayer.value.src = audioUrl

        // 如果之前在播放，继续播放
        if (wasPlaying) {
          setTimeout(() => {
            audioPlayer.value.play()
          }, 100)
        }

        currentTime.value = newTime // 立即更新UI
      }, 100)

    } catch (error) {
      console.error('快进失败:', error)
      uni.showToast({
        title: '快进失败',
        icon: 'none'
      })
    }
  }
}

// 进度条拖拽相关状态
const isDragging = ref(false)
const dragStartX = ref(0)
const dragStartPercent = ref(0)

const onProgressClick = (e) => {
  if (audioPlayer.value && duration.value > 0 && !isDragging.value) {
    try {
      // 在小程序中使用 createSelectorQuery 获取元素信息
      const query = uni.createSelectorQuery()
      query.select('#progress-track').boundingClientRect((rect) => {
        if (rect) {
          // 获取点击位置相对于进度条的百分比
          const clickX = e.detail.x - rect.left
          const percent = Math.max(0, Math.min(1, clickX / rect.width))
          seekToPercent(percent)
        } else {
          console.error('无法获取进度条元素信息')
          uni.showToast({
            title: '跳转失败',
            icon: 'none'
          })
        }
      }).exec()

    } catch (error) {
      console.error('进度条点击失败:', error)
      uni.showToast({
        title: '跳转失败',
        icon: 'none'
      })
    }
  }
}

// 触摸开始
const onProgressTouchStart = (e) => {
  if (audioPlayer.value && duration.value > 0) {
    isDragging.value = true
    dragStartX.value = e.touches[0].clientX
    dragStartPercent.value = progressPercent.value
    console.log('开始拖拽进度条')
  }
}

// 触摸移动
const onProgressTouchMove = (e) => {
  if (isDragging.value && audioPlayer.value && duration.value > 0) {
    try {
      const query = uni.createSelectorQuery()
      query.select('#progress-track').boundingClientRect((rect) => {
        if (rect) {
          const currentX = e.touches[0].clientX
          const deltaX = currentX - dragStartX.value
          const deltaPercent = (deltaX / rect.width) * 100
          const newPercent = Math.max(0, Math.min(100, dragStartPercent.value + deltaPercent))

          // 实时更新UI显示
          currentTime.value = (newPercent / 100) * duration.value
        }
      }).exec()
    } catch (error) {
      console.error('拖拽进度条失败:', error)
    }
  }
}

// 触摸结束
const onProgressTouchEnd = (e) => {
  if (isDragging.value && audioPlayer.value && duration.value > 0) {
    try {
      const query = uni.createSelectorQuery()
      query.select('#progress-track').boundingClientRect((rect) => {
        if (rect) {
          const currentX = e.changedTouches[0].clientX
          const deltaX = currentX - dragStartX.value
          const deltaPercent = (deltaX / rect.width) * 100
          const newPercent = Math.max(0, Math.min(100, dragStartPercent.value + deltaPercent))
          const percent = newPercent / 100

          seekToPercent(percent)
        }
      }).exec()
    } catch (error) {
      console.error('结束拖拽失败:', error)
    }

    isDragging.value = false
    console.log('结束拖拽进度条')
  }
}

// 跳转到指定百分比位置
const seekToPercent = (percent) => {
  const audioUrl = currentAudio.value.audioUrl || meditationDetail.value.audioUrl
  if (audioPlayer.value && duration.value > 0 && audioUrl) {
    const seekTime = duration.value * percent
    console.log('跳转到:', seekTime, '百分比:', percent)

    // 使用和快进/回退相同的跳转方法
    const wasPlaying = isPlaying.value
    const currentTitle = currentAudio.value.audioName || meditationDetail.value.title || '冥想音频'
    const currentSinger = '喜欢心理'
    const currentCover = currentAudio.value.coverImage || meditationDetail.value.coverImage || ''

    // 验证音频URL
    if (!audioUrl) {
      console.error('音频URL为空，无法跳转')
      uni.showToast({
        title: '音频加载失败',
        icon: 'none'
      })
      return
    }

    // 停止播放
    audioPlayer.value.stop()

    // 重新设置音频信息和开始时间
    setTimeout(() => {
      audioPlayer.value.title = currentTitle
      audioPlayer.value.singer = currentSinger
      audioPlayer.value.coverImgUrl = currentCover
      audioPlayer.value.startTime = seekTime
      audioPlayer.value.src = audioUrl

      // 如果之前在播放，继续播放
      if (wasPlaying) {
        setTimeout(() => {
          audioPlayer.value.play()
        }, 100)
      }

      currentTime.value = seekTime // 立即更新UI
    }, 100)
  }
}

// 倍速相关方法（暂时隐藏）
// const toggleSpeed = () => {
//   // 同步当前播放速度到选择器
//   syncSelectedSpeed()
//   speedPopup.value?.open()
// }

// const selectSpeed = (speed) => {
//   selectedSpeed.value = speed
//   playbackRate.value = speed
//
//   console.log('设置倍速:', speed)
//
//   // 立即应用倍速设置
//   if (audioPlayer.value) {
//     try {
//       if ('playbackRate' in audioPlayer.value) {
//         audioPlayer.value.playbackRate = speed
//         console.log('倍速设置为:', speed)
//
//         // 验证设置结果
//         setTimeout(() => {
//           const actualRate = audioPlayer.value.playbackRate
//           console.log('实际倍速:', actualRate)
//           if (Math.abs(actualRate - speed) < 0.01) {
//             uni.showToast({
//               title: `播放速度: ${speed}x`,
//               icon: 'success'
//             })
//           } else {
//             uni.showToast({
//               title: '倍速设置失败',
//               icon: 'none'
//             })
//           }
//         }, 100)
//       } else {
//         console.log('不支持倍速功能')
//         uni.showToast({
//           title: '当前设备不支持倍速播放',
//           icon: 'none'
//         })
//       }
//     } catch (error) {
//       console.error('设置倍速失败:', error)
//       uni.showToast({
//         title: '倍速设置失败',
//         icon: 'none'
//       })
//     }
//   }
// }

// const confirmSpeed = () => {
//   // 关闭弹窗即可，倍速已经在选择时应用了
//   closeSpeed()
// }

// const closeSpeed = () => {
//   speedPopup.value?.close()
// }

const toggleTimer = () => {
  timerPopup.value?.open()
}

const selectTimer = (value) => {
  selectedTimer.value = value
}

const confirmTimer = () => {
  if (selectedTimer.value > 0) {
    remainingTime.value = selectedTimer.value * 60 // 转换为秒
    startTimer()
    uni.showToast({
      title: `已设置${selectedTimer.value}分钟后关闭`,
      icon: 'success'
    })
  } else {
    stopTimer()
  }
  closeTimer()
}

const closeTimer = () => {
  timerPopup.value?.close()
}

const startTimer = () => {
  stopTimer()
  timerInterval.value = setInterval(() => {
    remainingTime.value--
    if (remainingTime.value <= 0) {
      pauseAudio()
      stopTimer()
      uni.showToast({
        title: '定时结束，已暂停播放',
        icon: 'none'
      })
    }
  }, 1000)
}

const stopTimer = () => {
  if (timerInterval.value) {
    clearInterval(timerInterval.value)
    timerInterval.value = null
  }
}

const toggleFavorite = () => {
  isFavorited.value = !isFavorited.value
  uni.showToast({
    title: isFavorited.value ? '已收藏' : '已取消收藏',
    icon: 'success'
  })
}

// 播放模式切换
const togglePlayMode = () => {
  playMode.value = (playMode.value + 1) % 3
  const modes = ['顺序播放', '单曲循环', '随机播放']
  uni.showToast({
    title: modes[playMode.value],
    icon: 'none'
  })
}

// 获取播放模式图标
const getPlayModeIcon = () => {
  const icons = [
    '/static/icon/meditation/曲线 1.png', // 顺序播放
    '/static/icon/meditation/形状 9.png', // 单曲循环
    '/static/icon/meditation/形状 11.png' // 随机播放
  ]
  return icons[playMode.value]
}

// 获取播放模式文本
const getPlayModeText = () => {
  const modes = ['正序', '单曲', '随机']
  return modes[playMode.value]
}

// 打开播放列表
const togglePlaylist = () => {
  playlistPopup.value?.open()
}

// 播放指定曲目
const playTrack = async (index) => {
  if (index >= 0 && index < playableAudios.value.length) {
    currentTrackIndex.value = index
    const audio = playableAudios.value[index]
    currentAudio.value = audio
    currentAudioId.value = audio.id

    // 停止当前播放
    if (audioPlayer.value) {
      audioPlayer.value.stop()
    }

    // 重置播放状态
    currentTime.value = 0
    duration.value = audio.canPlayFull ? audio.duration : audio.playableDuration

    // 增加播放次数
    try {
      await playAudio(audio.id)
    } catch (error) {
      console.error('增加播放次数失败:', error)
    }

    // 开始播放新曲目
    setTimeout(() => {
      initAudioPlayer()
      audioPlayer.value?.play()
    }, 100)

    // 关闭播放列表
    playlistPopup.value?.close()
  }
}

// 上一首
const playPrevious = () => {
  if (playableAudios.value.length === 0) return

  let prevIndex
  if (playMode.value === 2) { // 随机播放
    prevIndex = Math.floor(Math.random() * playableAudios.value.length)
  } else {
    prevIndex = currentTrackIndex.value - 1
    if (prevIndex < 0) {
      prevIndex = playableAudios.value.length - 1
    }
  }
  playTrack(prevIndex)
}

// 下一首
const playNext = () => {
  if (playableAudios.value.length === 0) return

  let nextIndex
  if (playMode.value === 2) { // 随机播放
    nextIndex = Math.floor(Math.random() * playableAudios.value.length)
  } else {
    nextIndex = currentTrackIndex.value + 1
    if (nextIndex >= playableAudios.value.length) {
      nextIndex = 0
    }
  }
  playTrack(nextIndex)
}

// 保存播放记录
const savePlayRecord = async (isCompleted = false) => {
  if (!currentAudio.value.id || !meditationDetail.value.id) return

  try {
    const progressPercentage = duration.value > 0 ? (currentTime.value / duration.value) * 100 : 0

    const recordData = {
      userId: userStore.userInfo?.id,
      meditationId: meditationDetail.value.id,
      audioId: currentAudio.value.id,
      audioName: currentAudio.value.audioName,
      durationPlayed: Math.floor(currentTime.value),
      progressPercentage: Math.round(progressPercentage * 100) / 100,
      lastPosition: Math.floor(currentTime.value),
      isCompleted: isCompleted ? 1 : 0
    }

    await saveMeditationRecord(recordData)
    console.log('播放记录保存成功')
  } catch (error) {
    console.error('保存播放记录失败:', error)
  }
}



// 销毁音频播放器
const destroyAudioPlayer = () => {
  if (audioPlayer.value) {
    try {
      audioPlayer.value.stop()
      console.log('音频播放器已停止')
    } catch (error) {
      console.log('停止音频播放器失败:', error)
    }
    audioPlayer.value = null
  }
}

// 生命周期
onLoad((options) => {
  meditationId.value = options.id
  if (meditationId.value) {
    loadMeditationDetail()
  }
})

onUnmounted(async () => {
  // 保存播放记录
  if (userStore.isLoggedIn && currentTime.value > 0) {
    await savePlayRecord(false)
  }

  stopTimer()
  destroyAudioPlayer()
})
</script>

<style lang="scss" scoped>
.meditation-player {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 120rpx 60rpx 80rpx;
  align-items: center;
}

.cover-container {
  margin-bottom: 60rpx;
  width: 100%;

  .cover-wrapper {
    position: relative;
    width: 100%;
    height: 480rpx;
    border-radius: 50%;
    overflow: hidden;

    .cover-image {
      width: 100%;
      height: 100%;
    }

    .play-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(2rpx);

      .play-overlay-bg {
        width: 580rpx;
        height: 400rpx;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      .play-overlay-icon {
        width: 480rpx;
        height: 480rpx;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
      }

      .play-overlay-circle {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border: 12rpx #fff solid;
        border-radius: 50%;
        width: 156rpx;
        height: 156rpx;
        z-index: 2;
      }

      .play-overlay-dot {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border-radius: 50%;
        width: 72rpx;
        height: 72rpx;
        z-index: 3;
        background-color: #fff;
      }

      .play-icon {
        width: 80rpx;
        height: 80rpx;
      }
    }
  }
}

.meditation-info {
  text-align: center;
  margin-bottom: 80rpx;

  .meditation-title {
    font-size: 36rpx;
    font-weight: 700;
    color: #000;
    margin-bottom: 23rpx;
  }

  .meditation-subtitle {
    font-size: 24rpx;
    color: #8A8788;
  }
}

.progress-section {
  width: 100%;
  margin-bottom: 63rpx;

  .time-display {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20rpx;
    font-size: 26rpx;
    color: #666;
  }

  .progress-bar {
    width: 100%;
    height: 60rpx;
    display: flex;
    align-items: center;

    .progress-track {
      position: relative;
      width: 100%;
      height: 6rpx;
      background-color: #E5E5E5;
      border-radius: 3rpx;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #FF6B9D 0%, #FF8A80 100%);
        border-radius: 3rpx;
        transition: width 0.1s ease;
      }

      .progress-thumb {
        position: absolute;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 20rpx;
        height: 20rpx;
        background: linear-gradient(90deg, #FF6B9D 0%, #FF8A80 100%);
        border-radius: 50%;
      }
    }
  }
}

.control-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 40rpx;

  .side-controls {
    .side-btn {
      width: 80rpx;
      height: 80rpx;
      background: none;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 !important;
      border: none !important;

      &::after {
        border: none !important;
      }

      .side-icon {
        width: 48rpx;
        height: 48rpx;
      }
    }
  }

  .main-controls {
    display: flex;
    align-items: center;
    gap: 60rpx;

    .control-btn {
      width: 80rpx;
      height: 80rpx;
      background: none;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 !important;
      border: 0 !important;

      &::after {
        border: none !important;
      }

      .control-icon {
        width: 38rpx;
        height: 38rpx;
      }
    }

    .play-btn-main {
      width: 100rpx;
      height: 100rpx;
      background: none;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 !important;
      border: 0 !important;

      &::after {
        border: none !important;
      }

      .play-icon-main {
        width: 60rpx;
        height: 60rpx;
      }
    }
  }
}

.guidance-text {
  margin-top: 60rpx;

  .guidance-scroll {
    max-height: 300rpx;
    padding: 30rpx;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 20rpx;
    backdrop-filter: blur(10rpx);

    text {
      font-size: 28rpx;
      line-height: 1.6;
      color: #fff;
      opacity: 0.9;
    }
  }
}

.timer-content {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx;

  .timer-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    text-align: center;
    margin-bottom: 40rpx;
  }

  .timer-options {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
    margin-bottom: 40rpx;

    .timer-option {
      flex: 1;
      min-width: 120rpx;
      padding: 20rpx;
      text-align: center;
      background-color: #f8f8f8;
      border-radius: 12rpx;
      font-size: 28rpx;
      color: #666;

      &.active {
        background-color: #ff6b35;
        color: #fff;
      }
    }
  }

  .timer-actions {
    display: flex;
    gap: 20rpx;

    .timer-btn {
      flex: 1;
      padding: 24rpx;
      border-radius: 12rpx;
      font-size: 30rpx;
      border: none;

      &.cancel {
        background-color: #f8f8f8;
        color: #666;
      }

      &.confirm {
        background-color: #ff6b35;
        color: #fff;
      }
    }
  }
}

// 播放列表弹窗样式
.playlist-content {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;

  .playlist-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 40rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .playlist-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .playlist-order {
      font-size: 24rpx;
      color: #666;
      padding: 8rpx 16rpx;
      background: #f5f5f5;
      border-radius: 20rpx;
    }
  }

  .playlist-scroll {
    flex: 1;
    max-height: 60vh;

    .playlist-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 40rpx;
      border-bottom: 1rpx solid #f8f8f8;

      &.active {
        background: #f0f8ff;

        .track-title {
          color: #007AFF;
        }
      }

      &:last-child {
        border-bottom: none;
      }

      .track-info {
        display: flex;
        align-items: center;
        flex: 1;

        .track-number {
          font-size: 24rpx;
          color: #999;
          width: 60rpx;
          margin-right: 20rpx;
        }

        .track-details {
          flex: 1;

          .track-title {
            font-size: 28rpx;
            color: #333;
            margin-bottom: 8rpx;
            line-height: 1.2;
          }

          .track-duration {
            font-size: 22rpx;
            color: #999;
          }

          .trial-tag {
            font-size: 20rpx;
            color: #ff6b35;
            background: #fff3f0;
            padding: 2rpx 8rpx;
            border-radius: 8rpx;
            margin-left: 8rpx;
          }
        }
      }
    }
  }
}

/* 倍速相关样式暂时隐藏
.speed-content {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx;

  .speed-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    text-align: center;
    margin-bottom: 40rpx;
  }

  .speed-options {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
    margin-bottom: 40rpx;

    .speed-option {
      flex: 1;
      min-width: 120rpx;
      padding: 20rpx;
      text-align: center;
      background-color: #f8f8f8;
      border-radius: 12rpx;
      font-size: 28rpx;
      color: #666;

      &.active {
        background-color: #ff6b35;
        color: #fff;
      }
    }
  }

  .speed-actions {
    display: flex;
    gap: 20rpx;

    .speed-btn {
      flex: 1;
      padding: 24rpx;
      border-radius: 12rpx;
      font-size: 30rpx;
      border: none;

      &.cancel {
        background-color: #f8f8f8;
        color: #666;
      }

      &.confirm {
        background-color: #ff6b35;
        color: #fff;
      }
    }
  }
}
*/
</style>
