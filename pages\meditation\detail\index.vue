<template>
  <view class="meditation-detail">
    <view class="meditation-content">
      <!-- 冥想头部信息 -->
      <view class="meditation-header">
        <view class="header-content">
          <image :src="meditationDetail.coverImage || defaultCover" class="cover-image" mode="aspectFill"></image>
          <view class="header-info">
            <view class="meditation-title">{{ meditationDetail.title }}</view>
            <view class="meditation-subtitle">{{ meditationDetail.subtitle }}</view>
            <view class="meditation-meta">
              <text class="difficulty">入门</text>
              <text class="duration">时长：{{ meditationDetail.totalDuration || 0 }}min</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 简介部分 -->
      <view class="intro-section">
        <view class="section-title">简介</view>
        <view class="intro-content">{{ meditationDetail.description || '暂无简介' }}</view>
      </view>

      <!-- 列表部分 -->
      <view class="playlist-section">
        <view class="section-header">
          <view class="section-title">列表</view>
          <view class="sort-button" @click="toggleSort">
            <text>正序</text>
            <uni-icons type="arrowdown" size="14" color="#999"></uni-icons>
          </view>
        </view>

        <view class="audio-list">
          <view v-for="(audio, index) in audioList" :key="audio.id" class="audio-item" @click="playAudio(audio, index)">
            <image :src="audio.coverImage || meditationDetail.coverImage || defaultCover" class="audio-cover"
              mode="aspectFill"></image>
            <view class="audio-info">
              <view class="audio-title-wrapper">
                <view class="audio-title">{{ audio.audioName }}</view>
                <view v-if="audio.isFree || !audio.needPurchase" class="free-tag">试听</view>
              </view>
              <view class="audio-duration">{{ formatDuration(audio.duration) }}</view>
            </view>
            <view class="play-button">
              <view class="play-icon"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <UniversalGoodsNav style="z-index: 1;" page-type="meditation" :detail-data="meditationDetail"
      :purchased="meditationDetail.purchased" :price="meditationDetail.price" :favorited="meditationDetail.favorited"
      :favorite-id="meditationDetail.favoriteId" @favorite="handleFavorite" @contact-service="handleContactService"
      @share="handleShare" @main-action="handleMainAction" />

    <!-- 支付弹框 -->
    <PaymentModal ref="paymentModal" :order-info="orderInfo" @close="onPaymentClose" @pay-success="onPaymentSuccess"
      @pay-fail="onPaymentFail" />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import {
  getMeditationWithAudios,
  createMeditationOrder
} from '@/api/meditation'
import { useUserStore } from '@/stores/user'
import PaymentModal from '@/components/PaymentModal/PaymentModal.vue'
import UniversalGoodsNav from '@/components/UniversalGoodsNav/UniversalGoodsNav.vue'
// 添加转发支持
import { getCurrentInstance, onUnmounted } from 'vue'

// 响应式数据
const meditationDetail = ref({})
const audioList = ref([])
const meditationId = ref(null)
const paymentModal = ref(null)
const orderInfo = ref({})
const sortAscending = ref(true)

// 默认图片
const defaultCover = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png'

const userStore = useUserStore()

// 处理收藏事件
const handleFavorite = (favoriteData) => {
  meditationDetail.value.favorited = favoriteData.favorited
  meditationDetail.value.favoriteId = favoriteData.favoriteId
}

// 处理客服事件
const handleContactService = () => {
  console.log('联系客服')
}

// 处理分享事件
const handleShare = (shareConfig) => {
  console.log('分享配置:', shareConfig)
  uni.showToast({
    title: '转发成功',
    icon: 'success'
  })
}

// 处理主要操作事件
const handleMainAction = ({ pageType }) => {
  if (!meditationDetail.value.purchased && meditationDetail.value.isFree !== 1 && meditationDetail.value.price > 0) {
    buyMeditation()
  } else {
    startMeditation()
  }
}

// 方法
const loadMeditationDetail = async () => {
  try {
    const res = await getMeditationWithAudios(meditationId.value)
    if (res.code === 200) {
      const data = res.data
      meditationDetail.value = data
      audioList.value = data.audios || []
    } else {
      uni.showToast({
        title: res.msg || '获取冥想详情失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取冥想详情失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

// 切换排序
const toggleSort = () => {
  sortAscending.value = !sortAscending.value
  audioList.value.reverse()
}

// 格式化时长
const formatDuration = (seconds) => {
  if (!seconds) return '00:00'
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`
}

// 播放音频
const playAudio = (audio, index) => {
  // 跳转到播放器页面
  uni.navigateTo({
    url: `/pages/meditation/player/index?id=${meditationId.value}&audioId=${audio.id}&audioIndex=${index}`
  })
}

const buyMeditation = async () => {
  if (!userStore.isLoggedIn) {
    uni.navigateTo({
      url: '/pages/login/login'
    })
    return
  }

  try {
    uni.showLoading({ title: '创建订单中...' })

    const res = await createMeditationOrder(meditationId.value)

    uni.hideLoading()

    if (res.code === 200) {
      // 设置订单信息并打开支付弹框
      orderInfo.value = {
        orderNo: res.data.orderNo,
        product: meditationDetail.value
      }
      paymentModal.value?.open()
    } else {
      uni.showToast({
        title: res.msg || '创建订单失败',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('创建订单失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

const startMeditation = () => {
  uni.navigateTo({
    url: `/pages/meditation/player/index?id=${meditationId.value}`
  })
}

const formatDate = (dateStr) => {
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 支付弹框事件处理
const onPaymentClose = () => {
  console.log('支付弹框关闭')
}

const onPaymentSuccess = (paymentData) => {
  console.log('支付成功:', paymentData)

  // 显示支付成功提示
  uni.showToast({
    title: '购买成功',
    icon: 'success',
    duration: 2000
  })

  // 更新冥想购买状态
  meditationDetail.value.purchased = true

  // 重新加载冥想详情以获取最新状态
  setTimeout(() => {
    loadMeditationDetail()
  }, 500)
}

const onPaymentFail = (error) => {
  console.log('支付失败:', error)
}

// 生命周期
onLoad((options) => {
  meditationId.value = options.id
  if (meditationId.value) {
    loadMeditationDetail()
  }
})


const shareConfig = ref(null)

// 监听转发事件
uni.$on('triggerShare', (config) => {
  shareConfig.value = config
  // #ifdef MP-WEIXIN
  uni.showToast({
    title: '请点击右上角分享',
    icon: 'none'
  })
  // #endif
})

// 页面卸载时移除监听
onUnmounted(() => {
  uni.$off('triggerShare')
})
</script>

<script>
// 支持转发的页面配置
export default {
  onShareAppMessage() {
    const currentInstance = getCurrentInstance()
    const shareConfig = currentInstance?.ctx?.shareConfig

    if (shareConfig) {
      return {
        title: shareConfig.title,
        path: shareConfig.path,
        imageUrl: shareConfig.imageUrl
      }
    }

    // 默认分享配置
    const meditationDetail = currentInstance?.ctx?.meditationDetail
    if (meditationDetail) {
      return {
        title: `推荐冥想：${meditationDetail.title}`,
        path: `pages/meditation/detail/index?id=${meditationDetail.id}`,
        imageUrl: meditationDetail.coverImage
      }
    }

    return {
      title: '熙桓心理冥想',
      path: 'pages/index/index'
    }
  },

  onShareTimeline() {
    const currentInstance = getCurrentInstance()
    const shareConfig = currentInstance?.ctx?.shareConfig

    if (shareConfig) {
      return {
        title: shareConfig.title,
        query: '',
        imageUrl: shareConfig.imageUrl
      }
    }

    // 默认分享配置
    const meditationDetail = currentInstance?.ctx?.meditationDetail
    if (meditationDetail) {
      return {
        title: `推荐冥想：${meditationDetail.title}`,
        query: `id=${meditationDetail.id}`,
        imageUrl: meditationDetail.coverImage
      }
    }

    return {
      title: '熙桓心理冥想'
    }
  }
}
</script>

<style lang="scss" scoped>
.meditation-detail {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 160rpx;

  .meditation-content {
    background-color: #f5f5f5;
    padding: 32rpx;
  }
}

.meditation-header {
  background-color: #fff;
  padding: 32rpx;
  margin-bottom: 20rpx;

  .header-content {
    display: flex;
    align-items: flex-start;
    gap: 24rpx;

    .cover-image {
      width: 186rpx;
      height: 186rpx;
      border-radius: 8rpx;
      flex-shrink: 0;
      margin-right: 22rpx;
    }

    .header-info {
      flex: 1;
      padding-top: 8rpx;

      .meditation-title {
        font-size: 40rpx;
        font-weight: 700;
        color: #000;
        margin-bottom: 27rpx;
      }

      .meditation-subtitle {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 20rpx;
        line-height: 1.4;
      }

      .meditation-meta {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .difficulty {
          font-size: 20rpx;
          color: #8A8788;
          background-color: #EBEBEB;
          padding: 6rpx 8rpx;
          border-radius: 4rpx;
        }

        .duration {
          font-size: 24rpx;
          color: #000;
        }
      }
    }
  }
}

.intro-section {
  background-color: #fff;
  padding: 32rpx;
  margin-bottom: 20rpx;

  .section-title {
    font-size: 28rpx;
    font-weight: 700;
    color: #000;
    margin-bottom: 23rpx;
  }

  .intro-content {
    font-size: 24rpx;
    color: #8A8788;
  }
}

.playlist-section {
  background-color: #fff;
  padding: 32rpx;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .sort-button {
      display: flex;
      align-items: center;
      gap: 8rpx;
      font-size: 26rpx;
      color: #999;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      background-color: #f8f8f8;
    }
  }

  .audio-list {
    .audio-item {
      display: flex;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1px solid #f0f0f0;
      height: 128rpx;

      &:last-child {
        border-bottom: none;
      }

      .audio-cover {
        width: 80rpx;
        height: 80rpx;
        border-radius: 8rpx;
        margin-right: 20rpx;
        flex-shrink: 0;
      }

      .audio-info {
        flex: 1;
        position: relative;

        .audio-title-wrapper {
          display: flex;
        }

        .audio-title {
          font-size: 28rpx;
          color: #000;
          margin-bottom: 8rpx;
          font-weight: 700;
        }

        .audio-duration {
          font-size: 24rpx;
          color: #ACA8AA;
        }

        .free-tag {
          position: absolute;
          top: 0;
          right: 0;
          font-size: 20rpx;
          color: #3f5fa5;
          background-color: #E8EBF4;
          padding: 2rpx 8rpx;
          border-radius: 8rpx;
        }
      }

      .play-button {
        padding: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .play-icon {
          width: 56rpx;
          height: 56rpx;
          background: #E7E7E7;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;

          &::after {
            content: '';
            width: 0;
            height: 0;
            border-left: 12rpx solid #afaaac;
            border-top: 8rpx solid transparent;
            border-bottom: 8rpx solid transparent;
            margin-left: 4rpx;
          }
        }
      }
    }
  }
}
</style>
