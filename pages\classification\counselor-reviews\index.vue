<template>
	<view class="reviews-container">

		<!-- 筛选选项 -->
		<view class="filter-section">
			<view class="filter-tags">
				<view class="filter-tag" :class="{ active: activeFilter === 'all' }" @click="setFilter('all')">
					<text>全部 {{ statistics.totalCount || 0 }}</text>
				</view>
				<view class="filter-tag" :class="{ active: activeFilter === 'high' }" @click="setFilter('high')">
					<text>情感困惑 {{ statistics.distribution?.['5'] || 0 }}</text>
				</view>
				<view class="filter-tag" :class="{ active: activeFilter === 'medium' }" @click="setFilter('medium')">
					<text>亲子教育 {{ statistics.distribution?.['4'] || 0 }}</text>
				</view>
				<view class="filter-tag" :class="{ active: activeFilter === 'low' }" @click="setFilter('low')">
					<text>人际关系 {{ statistics.distribution?.['3'] || 0 }}</text>
				</view>
				<view class="filter-tag" :class="{ active: activeFilter === 'latest' }" @click="setFilter('latest')">
					<text>成长创伤 {{ statistics.distribution?.['2'] || 0 }}</text>
				</view>
			</view>
		</view>

		<!-- 评价列表 -->
		<view class="reviews-list">
			<ReviewItem v-for="review in filteredReviews" :key="review.id" :review="review" :content-max-length="200"
				:show-logo="false" :show-reply="true" :show-card="true" class="review-item" :consultTypeLabel="'咨询类别'"
				:defaultConsultType="'情感焦虑'" />
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-if="filteredReviews.length === 0 && !loading">
			<image class="empty-icon" src="/static/images/empty-reviews.png" mode="aspectFit" />
			<text class="empty-text">暂无评价数据</text>
		</view>

		<!-- 加载状态 -->
		<view class="loading-state" v-if="loading">
			<uni-load-more :status="loadingStatus" />
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getConsultantReviews, getReviewStatistics } from '@/api/classification.js'
import ReviewItem from '@/components/ReviewItem/ReviewItem.vue'

// 页面参数
const consultantId = ref(null)

// 数据状态
const loading = ref(false)
const loadingStatus = ref('loading')
const reviews = ref([])
const statistics = ref({
	avgRating: 0,
	totalCount: 0,
	distribution: {}
})

// 筛选状态
const activeFilter = ref('all')

// 默认头像
const defaultAvatar = '/static/images/default-avatar.png'

// 页面加载
onLoad((options) => {
	consultantId.value = options.consultantId
	if (consultantId.value) {
		loadReviewsData()
		loadStatistics()
	}
})

// 加载评价数据
const loadReviewsData = async () => {
	try {
		loading.value = true
		loadingStatus.value = 'loading'

		const res = await getConsultantReviews(consultantId.value)

		if (res.code === 200) {
			reviews.value = res.data || []
		} else {
			// 使用测试数据
			reviews.value = [
				{
					id: 1,
					username: '张女士',
					rating: 5,
					content: '这是我们第二次咨询，随着交谈的深入，我们开始触碰到一些根本的问题，我相信老师对我们有了一个全面客观的认识，张老师看得比较清晰，期待她能帮我老婆走出去，我到更美好的自己。',
					reviewTime: '2025-07-08T10:30:00',
					consultType: '情感焦点',
					userAvatar: '',
					consultantReply: '感谢您的信任，我会继续陪伴您和您的爱人一起成长。'
				},
				{
					id: 2,
					username: '李先生',
					rating: 5,
					content: '老师很专业，能够准确理解我的问题，给出了很好的建议和指导。咨询过程中感受到了老师的耐心和专业性。',
					reviewTime: '2025-07-07T14:20:00',
					consultType: '情感焦点',
					userAvatar: ''
				},
				{
					id: 3,
					username: '王女士',
					rating: 4,
					content: '咨询师很有耐心，帮助我理清了很多思路，虽然问题还没有完全解决，但已经有了很大的改善。',
					reviewTime: '2025-07-06T16:45:00',
					consultType: '个人成长',
					userAvatar: ''
				}
			]
		}

		loading.value = false
		loadingStatus.value = 'noMore'
	} catch (error) {
		console.error('加载评价数据失败:', error)
		// 使用测试数据
		reviews.value = [
			{
				id: 1,
				username: '张女士',
				rating: 5,
				content: '这是我们第二次咨询，随着交谈的深入，我们开始触碰到一些根本的问题，我相信老师对我们有了一个全面客观的认识，张老师看得比较清晰，期待她能帮我老婆走出去，我到更美好的自己。',
				reviewTime: '2025-07-08T10:30:00',
				consultType: '情感焦点',
				userAvatar: ''
			}
		]
		loading.value = false
		loadingStatus.value = 'error'
	}
}

// 加载统计数据
const loadStatistics = async () => {
	try {
		const res = await getReviewStatistics(consultantId.value)

		if (res.code === 200) {
			statistics.value = res.data || {}
		} else {
			// 使用测试数据
			statistics.value = {
				avgRating: 4.8,
				totalCount: 156,
				distribution: {
					'5': 120,
					'4': 25,
					'3': 8,
					'2': 2,
					'1': 1
				}
			}
		}
	} catch (error) {
		console.error('加载统计数据失败:', error)
		// 使用测试数据
		statistics.value = {
			avgRating: 4.8,
			totalCount: 156,
			distribution: {
				'5': 120,
				'4': 25,
				'3': 8,
				'2': 2,
				'1': 1
			}
		}
	}
}

// 筛选后的评价列表
const filteredReviews = computed(() => {
	let filtered = [...reviews.value]

	switch (activeFilter.value) {
		case 'high':
			filtered = filtered.filter(review => review.rating >= 4)
			break
		case 'medium':
			filtered = filtered.filter(review => review.rating >= 2 && review.rating < 4)
			break
		case 'low':
			filtered = filtered.filter(review => review.rating < 2)
			break
		case 'latest':
			filtered = filtered.sort((a, b) => new Date(b.reviewTime) - new Date(a.reviewTime))
			break
	}

	return filtered
})

// 设置筛选条件
const setFilter = (filter) => {
	activeFilter.value = filter
}

// 获取进度条宽度
const getProgressWidth = (count, total) => {
	if (!total || total === 0) return '0%'
	return `${(count / total * 100)}%`
}

// 格式化日期 - iOS 兼容版本
const formatDate = (dateStr) => {
	if (!dateStr) return ''

	// 将 "2025-07-27 12:16:10" 格式转换为 iOS 兼容的格式
	// iOS 支持: "yyyy/MM/dd"、"yyyy/MM/dd HH:mm:ss"、"yyyy-MM-dd"、"yyyy-MM-ddTHH:mm:ss"
	let formattedDateStr = dateStr

	// 如果是 "yyyy-MM-dd HH:mm:ss" 格式，转换为 "yyyy/MM/dd HH:mm:ss"
	if (dateStr.includes(' ') && dateStr.includes('-')) {
		formattedDateStr = dateStr.replace(/-/g, '/')
	}
	// 如果只是 "yyyy-MM-dd" 格式，转换为 "yyyy/MM/dd"
	else if (dateStr.includes('-') && !dateStr.includes(' ')) {
		formattedDateStr = dateStr.replace(/-/g, '/')
	}

	const date = new Date(formattedDateStr)

	// 检查日期是否有效
	if (isNaN(date.getTime())) {
		console.warn('Invalid date format:', dateStr)
		return dateStr // 返回原始字符串作为备用
	}

	return `${date.getFullYear()}/${String(date.getMonth() + 1).padStart(2, '0')}/${String(date.getDate()).padStart(2, '0')}`
}

// 用户名脱敏
const maskUsername = (username) => {
	if (!username) return '匿名用户'
	if (username.length <= 2) return username
	return username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1)
}
</script>

<style lang="scss" scoped>
.reviews-container {
	min-height: 100vh;
}

.statistics-section {
	background: white;
	padding: 40rpx;
	margin-bottom: 20rpx;

	.rating-overview {
		display: flex;
		align-items: flex-start;
		gap: 40rpx;

		.avg-rating {
			display: flex;
			flex-direction: column;
			align-items: center;

			.rating-number {
				font-size: 60rpx;
				font-weight: bold;
				color: #ff6b35;
				line-height: 1;
			}

			.stars {
				margin-top: 10rpx;
			}
		}

		.rating-info {
			flex: 1;

			.total-reviews {
				font-size: 28rpx;
				color: #666;
				margin-bottom: 20rpx;
				display: block;
			}

			.rating-distribution {
				.distribution-item {
					display: flex;
					align-items: center;
					margin-bottom: 10rpx;
					font-size: 24rpx;

					.star-label {
						width: 60rpx;
						color: #666;
					}

					.progress-bar {
						flex: 1;
						height: 12rpx;
						background: #f0f0f0;
						border-radius: 6rpx;
						margin: 0 20rpx;
						overflow: hidden;

						.progress-fill {
							height: 100%;
							background: #ff6b35;
							transition: width 0.3s ease;
						}
					}

					.count {
						width: 40rpx;
						text-align: right;
						color: #666;
					}
				}
			}
		}
	}
}

.filter-section {
	background: white;
	padding: 24rpx 32rpx;
	margin-bottom: 20rpx;

	.filter-tags {
		display: flex;
		flex-wrap: wrap;
		gap: 24rpx;

		.filter-tag {
			padding: 14rpx 22rpx;
			border-radius: 8rpx;
			font-size: 24rpx;
			background: #F7F7F7;
			color: #8A8788;
			transition: all 0.3s ease;

			&.active {
				background: #b85a9b;
				color: white;
			}

			text {
				font-size: 24rpx;
			}
		}
	}
}

.reviews-list {

	.review-item {

		&:last-child {
			border-bottom: none;
		}
	}
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;

	.empty-icon {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 40rpx;
		opacity: 0.5;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999;
	}
}

.loading-state {
	padding: 40rpx;
}
</style>
